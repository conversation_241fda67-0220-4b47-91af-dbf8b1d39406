import axiosInstance from '../components/utils/AxiosInstance';

// Note: We're using axiosInstance which has the correct base URL configured

export const userService = {
  // Get users with optional filters
  getUsers: async (filters = {}) => {
    try {
      const params = new URLSearchParams();

      // Add filters to params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, String(value));
        }
      });

      try {
        const response = await axiosInstance.get('/users/', { params });
        return response.data;
      } catch (apiError: any) {
        console.error('API Error details:', {
          status: apiError.response?.status,
          statusText: apiError.response?.statusText,
          data: apiError.response?.data,
          message: apiError.message
        });
        throw apiError;
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      // Return an empty object with the expected structure instead of an empty array
      return { results: [], count: 0 };
    }
  },

  // Get a single user by ID
  getUser: async (id: number | string) => {
    try {
      const response = await axiosInstance.get(`/users/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching user with ID ${id}:`, error);
      throw error;
    }
  },

  // Create a new user (admin-assisted registration)
  createUser: async (userData: any) => {
    try {
      // Determine the correct endpoint based on user type
      let endpoint;

      // Map user types to their specific API endpoints
      const userTypeEndpoints: Record<string, string> = {
        'student': '/users/students/',
        'teacher': '/users/teachers/',
        'parent': '/users/parents/',
        'staff': '/users/staff/',
        'school_admin': '/users/school-admins/',
        'deputy_principal': '/users/deputy-principals/',
        'branch_admin': '/users/branch-admins/',
        'department_head': '/users/department-heads/',
        'ict_admin': '/users/ict-admins/',
        'librarian': '/users/librarians/',
        'counselor': '/users/counselors/',
        'accountant': '/users/accountants/',
        'system_admin': '/users/admins/',
        // Add other user types as needed
      };

      // Get the endpoint for the user type or use the default create endpoint
      // First check if userData is FormData
      let userType = '';
      if (userData instanceof FormData) {
        userType = userData.get('user_type') as string;
      } else {
        userType = userData.user_type;
      }

      endpoint = userType && userTypeEndpoints[userType] ? userTypeEndpoints[userType] : '/users/create/';

      console.log(`Creating user of type ${userType} using endpoint: ${endpoint}`);

      // Format data based on user type if needed
      let formattedData = userData;

      // If userData is FormData, we need to ensure it has the right fields for each user type
      if (userData instanceof FormData) {
        const userType = userData.get('user_type') as string;

        // Add any user type specific formatting here
        switch(userType) {
          case 'parent':
            // Ensure children field is properly formatted for parent users
            // The backend expects an array of student IDs
            break;

          case 'teacher':
            // Ensure teacher-specific fields are properly formatted
            // For assigned_classes, ensure it's an array of numbers
            const assignedClasses = userData.get('assigned_classes');
            if (assignedClasses) {
              // Remove existing assigned_classes entries
              userData.delete('assigned_classes');

              // If it's a string representation of an array, parse it
              if (typeof assignedClasses === 'string' && assignedClasses.startsWith('[')) {
                try {
                  const classesArray = JSON.parse(assignedClasses);
                  // Add each class ID as a separate entry
                  classesArray.forEach((classId: number) => {
                    userData.append('assigned_classes', classId.toString());
                  });
                  console.log('Parsed assigned_classes:', classesArray);
                } catch (e) {
                  console.error('Error parsing assigned_classes:', e);
                }
              } else if (Array.isArray(assignedClasses)) {
                // If it's already an array, add each item
                assignedClasses.forEach((classId: number) => {
                  userData.append('assigned_classes', classId.toString());
                });
                console.log('Array assigned_classes:', assignedClasses);
              } else {
                // If it's a single value, add it
                userData.append('assigned_classes', assignedClasses.toString());
                console.log('Single assigned_classes:', assignedClasses);
              }
            }
            break;

          case 'student':
            // Ensure student-specific fields are properly formatted
            break;

          case 'school_admin':
          case 'deputy_principal':
          case 'branch_admin':
          case 'department_head':
          case 'ict_admin':
            // Ensure admin-specific fields are properly formatted
            break;

          default:
            // Default formatting
            break;
        }
      }

      // Special handling for FormData objects (multipart/form-data)
      const isFormData = userData instanceof FormData;
      const headers = isFormData ?
        { 'Content-Type': 'multipart/form-data' } :
        { 'Content-Type': 'application/json' };

      const response = await axiosInstance.post(endpoint, formattedData, { headers });
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  // Bulk create users from CSV
  bulkCreateUsers: async (formData: FormData) => {
    try {
      const response = await axiosInstance.post('/users/bulk-create/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error bulk creating users:', error);
      throw error;
    }
  },

  // Generate registration tokens for QR codes
  generateRegistrationTokens: async (userIds: number[]) => {
    try {
      const response = await axiosInstance.post('/users/generate-tokens/', { user_ids: userIds });
      return response.data;
    } catch (error) {
      console.error('Error generating registration tokens:', error);
      throw error;
    }
  },

  // Verify a registration token
  verifyRegistrationToken: async (token: string) => {
    try {
      const response = await axiosInstance.get(`/users/verify-token/${token}/`);
      return response.data;
    } catch (error) {
      console.error('Error verifying registration token:', error);
      throw error;
    }
  },

  // Complete registration with token
  completeRegistration: async (token: string, userData: any) => {
    try {
      const response = await axiosInstance.post(`/users/complete-registration/${token}/`, userData);
      return response.data;
    } catch (error) {
      console.error('Error completing registration:', error);
      throw error;
    }
  },

  // Update user
  updateUser: async (id: number | string, userData: any) => {
    try {
      const response = await axiosInstance.patch(`/users/${id}/`, userData);
      return response.data;
    } catch (error) {
      console.error(`Error updating user with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete user
  deleteUser: async (id: number | string) => {
    try {
      const response = await axiosInstance.delete(`/users/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting user with ID ${id}:`, error);
      throw error;
    }
  },

  // Reset user password
  resetPassword: async (id: number | string) => {
    try {
      const response = await axiosInstance.post(`/users/${id}/reset-password/`);
      return response.data;
    } catch (error) {
      console.error(`Error resetting password for user with ID ${id}:`, error);
      throw error;
    }
  },

  // Get user counts by type for dashboard metrics
  getUserCounts: async (schoolId?: number | string, branchId?: number | string) => {
    try {
      console.log(`📊 Fetching user counts for school=${schoolId}, branch=${branchId}`);

      const params = new URLSearchParams();
      if (schoolId) params.append('school', schoolId.toString());
      if (branchId) params.append('branch', branchId.toString());

      const queryString = params.toString() ? `?${params.toString()}` : '';

      // Use the new dedicated user counts endpoint
      const response = await axiosInstance.get(`/core/users/counts/${queryString}`);

      console.log(`✅ User counts received:`, response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ Error fetching user counts:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data
      });

      // Return default counts if API fails
      return {
        totalStudents: 0,
        totalTeachers: 0,
        totalParents: 0,
        totalStaff: 0,
        totalAdmins: 0
      };
    }
  },

  // Get current user profile
  getCurrentUser: async () => {
    try {
      const response = await axiosInstance.get('/users/profile/');
      return response.data;
    } catch (error) {
      console.error('Error fetching current user profile:', error);
      throw error;
    }
  },

  // Update user profile
  updateProfile: async (userData: any) => {
    try {
      // Handle FormData vs JSON
      const isFormData = userData instanceof FormData;
      const headers = isFormData ?
        { 'Content-Type': 'multipart/form-data' } :
        { 'Content-Type': 'application/json' };

      const response = await axiosInstance.patch('/users/profile/', userData, { headers });
      return response.data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },

  // Upload profile picture
  uploadProfilePicture: async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('profile_picture', file);

      const response = await axiosInstance.post('/users/me/profile-image/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data.profile_picture_url;
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      throw error;
    }
  }
};

export default userService;

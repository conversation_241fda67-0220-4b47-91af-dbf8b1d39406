#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_api_endpoints():
    print("🔍 Testing API Endpoints for School Assignment...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    headers = {'Authorization': f'Bearer {access_token}'}
    
    # Test 1: Schools API
    print(f"\n🔍 Testing Schools API...")
    schools_url = 'http://localhost:8000/api/schools/schools/'
    
    try:
        schools_response = requests.get(schools_url, headers=headers, timeout=10)
        print(f"   📡 Schools API Response: {schools_response.status_code}")
        
        if schools_response.status_code == 200:
            schools_data = schools_response.json()
            schools_count = len(schools_data.get('results', []))
            print(f"   ✅ Schools API successful: {schools_count} schools found")
            
            if schools_count > 0:
                sample_school = schools_data['results'][0]
                print(f"   📋 Sample school: {sample_school.get('name')} (ID: {sample_school.get('id')})")
            else:
                print(f"   ⚠️ No schools found in database")
        else:
            print(f"   ❌ Schools API failed: {schools_response.status_code}")
            print(f"   Error: {schools_response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Schools API request failed: {str(e)}")
    
    # Test 2: Branches API
    print(f"\n🔍 Testing Branches API...")
    branches_url = 'http://localhost:8000/api/schools/branch/'
    
    try:
        branches_response = requests.get(branches_url, headers=headers, timeout=10)
        print(f"   📡 Branches API Response: {branches_response.status_code}")
        
        if branches_response.status_code == 200:
            branches_data = branches_response.json()
            branches_count = len(branches_data.get('results', []))
            print(f"   ✅ Branches API successful: {branches_count} branches found")
            
            if branches_count > 0:
                sample_branch = branches_data['results'][0]
                school_info = sample_branch.get('school')
                print(f"   📋 Sample branch: {sample_branch.get('name')} (ID: {sample_branch.get('id')})")
                print(f"      - School info: {school_info} (type: {type(school_info)})")
                print(f"      - Branch structure: {list(sample_branch.keys())}")
            else:
                print(f"   ⚠️ No branches found in database")
        else:
            print(f"   ❌ Branches API failed: {branches_response.status_code}")
            print(f"   Error: {branches_response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Branches API request failed: {str(e)}")
    
    # Test 3: Student with assignment
    print(f"\n🔍 Testing Student Data Structure...")
    student = Student.objects.first()
    if student:
        student_url = f'http://localhost:8000/api/users/students/{student.user.id}/'
        
        try:
            student_response = requests.get(student_url, headers=headers, timeout=10)
            print(f"   📡 Student API Response: {student_response.status_code}")
            
            if student_response.status_code == 200:
                student_data = student_response.json()
                print(f"   ✅ Student API successful")
                print(f"   📋 Student: {student_data.get('first_name')} {student_data.get('last_name')}")
                
                # Check school_branch structure
                school_branch = student_data.get('school_branch')
                print(f"   🔍 school_branch field: {school_branch}")
                print(f"   🔍 school_branch type: {type(school_branch)}")
                
                if school_branch:
                    if isinstance(school_branch, dict):
                        print(f"      - Branch ID: {school_branch.get('id')}")
                        print(f"      - Branch name: {school_branch.get('name')}")
                        print(f"      - School info: {school_branch.get('school')}")
                    else:
                        print(f"      - Branch ID (direct): {school_branch}")
                
                # Check for school_branch_id field
                school_branch_id = student_data.get('school_branch_id')
                print(f"   🔍 school_branch_id field: {school_branch_id}")
                
            else:
                print(f"   ❌ Student API failed: {student_response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Student API request failed: {str(e)}")
    else:
        print(f"   ⚠️ No students found for testing")

    print(f"\n🎯 API endpoints test complete!")

if __name__ == "__main__":
    test_api_endpoints()

#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student, SchoolBranch
from core.models import CustomUser

def test_school_assignment_fix():
    print("🔍 Testing School Assignment Fix...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    headers = {'Authorization': f'Bearer {access_token}'}
    
    # Step 1: Get an unassigned student
    print(f"\n📋 Step 1: Finding an unassigned student...")
    unassigned_student = Student.objects.filter(school_branch__isnull=True).first()
    if not unassigned_student:
        print("   ⚠️ No unassigned students found, using first student")
        unassigned_student = Student.objects.first()
    
    if not unassigned_student:
        print("   ❌ No students found for testing")
        return
    
    print(f"   📋 Testing with: {unassigned_student.user.first_name} {unassigned_student.user.last_name}")
    print(f"   📋 Current assignment: {unassigned_student.school_branch}")
    
    # Step 2: Get a school branch to assign to
    print(f"\n📋 Step 2: Getting a school branch for assignment...")
    target_branch = SchoolBranch.objects.first()
    if not target_branch:
        print("   ❌ No school branches found for assignment")
        return
    
    print(f"   🎯 Target branch: {target_branch.name}")
    print(f"   🎯 Target school: {target_branch.school.name}")
    print(f"   🎯 Branch ID: {target_branch.id}")
    print(f"   🎯 School ID: {target_branch.school.id}")
    
    # Step 3: Assign student to branch
    print(f"\n📋 Step 3: Assigning student to branch...")
    assignment_url = f'http://localhost:8000/api/users/students/{unassigned_student.user.id}/'
    assignment_data = {'school_branch_id': target_branch.id}
    
    try:
        assignment_response = requests.patch(assignment_url, json=assignment_data, headers=headers, timeout=10)
        print(f"   📡 Assignment API Response: {assignment_response.status_code}")
        
        if assignment_response.status_code == 200:
            print(f"   ✅ Assignment successful!")
        else:
            print(f"   ❌ Assignment failed: {assignment_response.status_code}")
            print(f"   Error: {assignment_response.text[:200]}")
            return
    except Exception as e:
        print(f"   ❌ Assignment request failed: {str(e)}")
        return
    
    # Step 4: Fetch student data to verify school information is included
    print(f"\n📋 Step 4: Verifying school information in API response...")
    student_url = f'http://localhost:8000/api/users/students/{unassigned_student.user.id}/'
    
    try:
        student_response = requests.get(student_url, headers=headers, timeout=10)
        print(f"   📡 Student API Response: {student_response.status_code}")
        
        if student_response.status_code == 200:
            student_data = student_response.json()
            
            print(f"   ✅ Student data retrieved successfully")
            print(f"\n   🔍 Analyzing school_branch data structure:")
            
            school_branch = student_data.get('school_branch')
            if school_branch:
                print(f"      ✅ school_branch object exists")
                print(f"      📋 Branch ID: {school_branch.get('id')}")
                print(f"      📋 Branch name: {school_branch.get('name')}")
                
                # Check for school object (the fix)
                school = school_branch.get('school')
                if school:
                    print(f"      ✅ school object exists (FIX WORKING!)")
                    print(f"      📋 School ID: {school.get('id')}")
                    print(f"      📋 School name: {school.get('name')}")
                    
                    # Verify the IDs match what we assigned
                    if school.get('id') == target_branch.school.id:
                        print(f"      ✅ School ID matches target school!")
                    else:
                        print(f"      ❌ School ID mismatch: got {school.get('id')}, expected {target_branch.school.id}")
                        
                else:
                    print(f"      ❌ school object missing (FIX NOT WORKING)")
                
                # Check for school_name field (legacy)
                school_name = school_branch.get('school_name')
                if school_name:
                    print(f"      ✅ school_name field: {school_name}")
                else:
                    print(f"      ❌ school_name field missing")
                    
            else:
                print(f"      ❌ school_branch object missing")
                
            # Check school_branch_id field
            school_branch_id = student_data.get('school_branch_id')
            print(f"      📋 school_branch_id field: {school_branch_id}")
            
        else:
            print(f"   ❌ Student data retrieval failed: {student_response.status_code}")
            return
            
    except Exception as e:
        print(f"   ❌ Student data request failed: {str(e)}")
        return
    
    # Step 5: Test UnassignedStudents logic
    print(f"\n📋 Step 5: Testing UnassignedStudents logic...")
    
    # Simulate the frontend logic
    has_school = school_branch.get('school', {}).get('id') if school_branch else None
    has_branch = school_branch.get('id') if school_branch else None
    
    print(f"   🔍 Frontend logic simulation:")
    print(f"      - hasSchool: {bool(has_school)} (ID: {has_school})")
    print(f"      - hasBranch: {bool(has_branch)} (ID: {has_branch})")
    
    if has_school and has_branch:
        print(f"   ✅ Student should NOT appear in UnassignedStudents list!")
        issue_type = "Assigned"
    elif not has_school and not has_branch:
        print(f"   ❌ Student would appear as 'No School & No Branch'")
        issue_type = "No School & No Branch"
    elif not has_school:
        print(f"   ❌ Student would appear as 'No School'")
        issue_type = "No School"
    elif not has_branch:
        print(f"   ❌ Student would appear as 'No Branch'")
        issue_type = "No Branch"
    
    print(f"   📊 Issue type: {issue_type}")
    
    print(f"\n🎯 School assignment fix test complete!")
    print(f"\n📊 Summary:")
    print(f"   - Assignment API: {'✅' if assignment_response.status_code == 200 else '❌'}")
    print(f"   - Student data API: {'✅' if student_response.status_code == 200 else '❌'}")
    print(f"   - School object included: {'✅' if school else '❌'}")
    print(f"   - School ID correct: {'✅' if school and school.get('id') == target_branch.school.id else '❌'}")
    print(f"   - UnassignedStudents logic: {'✅' if issue_type == 'Assigned' else '❌'}")
    
    if school and school.get('id') == target_branch.school.id and issue_type == 'Assigned':
        print(f"\n🎉 FIX SUCCESSFUL! Students assigned to branches will now show proper school information!")
    else:
        print(f"\n❌ Fix needs more work. Check the issues above.")

if __name__ == "__main__":
    test_school_assignment_fix()

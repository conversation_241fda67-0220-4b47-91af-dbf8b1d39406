#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_deactivation_fix():
    print("🔍 Testing Student Deactivation Fix...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    
    # Get an active student for testing
    active_student = Student.objects.filter(user__is_active=True).first()
    if not active_student:
        print("❌ No active students found for testing")
        return
    
    print(f"\n📋 Testing with student: {active_student.user.first_name} {active_student.user.last_name}")
    print(f"   📋 Student User ID: {active_student.user.id}")
    print(f"   📋 Current status: {'Active' if active_student.user.is_active else 'Inactive'}")
    
    # Test 1: Deactivate using corrected format
    print(f"\n🔍 Test 1: Deactivating student (corrected format)...")
    deactivate_url = f'http://localhost:8000/api/users/students/{active_student.user.id}/'
    deactivate_data = {'is_active': False}  # Top-level field, not nested
    
    try:
        deactivate_response = requests.patch(deactivate_url, json=deactivate_data, headers=headers, timeout=10)
        print(f"   📡 Deactivate API Response: {deactivate_response.status_code}")
        
        if deactivate_response.status_code == 200:
            print(f"   ✅ Deactivation API call successful!")
            
            # Verify in database
            active_student.user.refresh_from_db()
            db_status = active_student.user.is_active
            print(f"   📋 Database status after update: {'Active' if db_status else 'Inactive'}")
            
            if not db_status:
                print(f"   ✅ SUCCESS: Student deactivated in database!")
            else:
                print(f"   ❌ FAILED: Student still active in database")
                return
        else:
            print(f"   ❌ Deactivation failed: {deactivate_response.status_code}")
            print(f"   Error: {deactivate_response.text[:200]}")
            return
    except Exception as e:
        print(f"   ❌ Deactivation request failed: {str(e)}")
        return
    
    # Test 2: Reactivate using corrected format
    print(f"\n🔍 Test 2: Reactivating student (corrected format)...")
    activate_data = {'is_active': True}  # Top-level field, not nested
    
    try:
        activate_response = requests.patch(deactivate_url, json=activate_data, headers=headers, timeout=10)
        print(f"   📡 Activate API Response: {activate_response.status_code}")
        
        if activate_response.status_code == 200:
            print(f"   ✅ Activation API call successful!")
            
            # Verify in database
            active_student.user.refresh_from_db()
            db_status = active_student.user.is_active
            print(f"   📋 Database status after update: {'Active' if db_status else 'Inactive'}")
            
            if db_status:
                print(f"   ✅ SUCCESS: Student reactivated in database!")
            else:
                print(f"   ❌ FAILED: Student still inactive in database")
        else:
            print(f"   ❌ Activation failed: {activate_response.status_code}")
            print(f"   Error: {activate_response.text[:200]}")
    except Exception as e:
        print(f"   ❌ Activation request failed: {str(e)}")
    
    # Test 3: Compare old vs new format
    print(f"\n🔍 Test 3: Comparing old vs new data format...")
    
    old_format = {'user': {'is_active': False}}
    new_format = {'is_active': False}
    
    print(f"   ❌ Old format (broken): {old_format}")
    print(f"   ✅ New format (working): {new_format}")
    
    print(f"\n🎯 Deactivation fix test complete!")
    print(f"\n📊 Summary:")
    print(f"   - Issue: Frontend was sending nested {{user: {{is_active: bool}}}}")
    print(f"   - Fix: Changed to top-level {{is_active: bool}}")
    print(f"   - Backend: StudentSerializer extracts is_active from top level")
    print(f"   - Result: {'✅ Working' if deactivate_response.status_code == 200 and activate_response.status_code == 200 else '❌ Still broken'}")
    
    print(f"\n🎉 Student activation/deactivation should now work in the frontend!")

if __name__ == "__main__":
    test_deactivation_fix()

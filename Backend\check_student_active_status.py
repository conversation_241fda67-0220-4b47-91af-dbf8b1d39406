#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from users.models import Student
from schools.models import SchoolBranch

def check_student_active_status():
    print("🔍 Checking Student Active Status...")
    
    # Check all students and their active status
    branches = SchoolBranch.objects.all()
    
    for branch in branches:
        students = Student.objects.filter(school_branch=branch)
        if students.exists():
            print(f"\n🏢 {branch.school.name} - {branch.name} (ID: {branch.id})")
            print(f"   📊 Total students: {students.count()}")
            
            for student in students:
                print(f"   👨‍🎓 {student.user.first_name} {student.user.last_name}")
                print(f"      - User ID: {student.user.id}")
                print(f"      - User Active: {student.user.is_active}")
                print(f"      - Admission Number: {student.admission_number}")
                
            # Check active vs inactive counts
            active_students = students.filter(user__is_active=True)
            inactive_students = students.filter(user__is_active=False)
            
            print(f"   ✅ Active students: {active_students.count()}")
            print(f"   ❌ Inactive students: {inactive_students.count()}")

if __name__ == "__main__":
    check_student_active_status()

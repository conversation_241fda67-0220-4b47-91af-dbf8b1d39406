from django.db import models, transaction
from django.utils.crypto import get_random_string
from django.core.validators import RegexValidator, MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from django.db.models.signals import post_save
from django.dispatch import receiver
from core.models import CustomUser
from schools.models import School, SchoolBranch
from datetime import datetime
import re
import random
from django.utils import timezone

# Common choices
SELECT_GENDER = [
    ('M', 'Male'),
    ('F', 'Female')
]

SELECT_RELATIONSHIP = [
    ('Father', 'Father'),
    ('Mother', 'Mother'),
    ('Guardian', 'Guardian'),
    ('Step Parent', 'Step Parent'),
    ('Other', 'Other')
]

SELECT_SUBJECT_TYPE = [
    ('Compulsory', 'Compulsory'),
    ('Optional', 'Optional'),
]

class UserProfileMixin(models.Model):
    """Mixin to provide common functionality for user profile models.
    
    Note: This is a mixin and should be used with another concrete model.
    It expects the concrete model to have a OneToOneField named 'user' linking to CustomUser.
    """
    
    class Meta:
        abstract = True # This is important for mixins

    def generate_unique_number(self, prefix, length=8):
        """Generate a unique number for the profile."""
        # Map model names to their number field names
        field_map = {
            'adminprofile': 'admin_number',
            'staff': 'staff_number',
            'teacher': 'teacher_number',
            'student': 'admission_number'
        }
        
        # Use lowercase model name from _meta to get the key
        model_name_lower = self._meta.model_name.lower() if hasattr(self._meta, 'model_name') and self._meta.model_name else ''
        field_name = field_map.get(model_name_lower)

        if not field_name:
            # Fallback or raise error if model name not in map
            # For now, let's raise an error as it indicates a misconfiguration
            raise ValueError(f"No number field defined for model {self._meta.model_name if hasattr(self._meta, 'model_name') else 'Unknown'}")
            
        while True:
            number = f"{prefix}{random.randint(10**(length-1), 10**length - 1)}"
            # Use self.__class__.objects to query the specific profile model
            if not self.__class__.objects.filter(**{field_name: number}).exists():
                return number
                
    # You can add common fields here if they exist across all profiles
    # created_at = models.DateTimeField(auto_now_add=True)
    # updated_at = models.DateTimeField(auto_now=True)


class AdminProfile(UserProfileMixin, models.Model):
    """Profile model for administrative users.

    This model is used for the following user types:
    - system_admin: System Administrator (ShuleXcel staff)
    - school_admin: School Administrator (Principal/Head of school)
    - deputy_principal: Deputy Principal
    - branch_admin: Branch Administrator
    - department_head: Department Head (HOD)
    - ict_admin: ICT Administrator
    - admin: Legacy admin type
    """
    # Direct link to CustomUser - primary_key=True implies unique=True and null=False
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, primary_key=True)

    school_branch = models.ForeignKey('schools.SchoolBranch', on_delete=models.CASCADE, related_name='admins', blank=True, null=True)
    admin_number = models.CharField(max_length=15, unique=True, blank=True, null=True)
    department = models.ForeignKey('academics.Department', on_delete=models.SET_NULL, null=True, blank=True)
    position = models.CharField(max_length=100, blank=True, null=True)
    date_hired = models.DateField(null=True, blank=True)
    bio = models.TextField(blank=True, null=True)
    phone_number = models.CharField(
        max_length=15, 
        blank=True, 
        null=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
            )
        ]
    )
    address = models.CharField(max_length=255, blank=True, null=True)
    date_of_birth = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=1, choices=SELECT_GENDER, blank=True, null=True)
    is_super_admin = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta(UserProfileMixin.Meta): # Inherit Meta from mixin
        app_label = 'users'
        verbose_name = 'Admin Profile'
        verbose_name_plural = 'Admin Profiles'
        ordering = ['-date_hired']

    def save(self, *args, **kwargs):
        print("AdminProfile save called") # Debug print
        # Generate admin_number only if it's a new profile and user is set (check self.pk)
        if not self.admin_number and self.pk:
            print("Generating AdminProfile number") # Debug print
            try:
                self.admin_number = self.generate_unique_number('ADM')
                print(f"Generated AdminProfile number: {self.admin_number}") # Debug print
            except Exception as e:
                print(f"Error generating AdminProfile number: {e}") # Debug print
                raise # Re-raise the exception
        super().save(*args, **kwargs)

    def __str__(self):
        # Access user's name directly via the 'user' OneToOneField
        # Safely access user and its methods
        if self.user:
            return f"{self.user.get_full_name()} ({self.admin_number or 'N/A'})"
        return f"Admin Profile ({self.admin_number or 'N/A'})"


class Staff(UserProfileMixin, models.Model):
    """Profile model for staff members.

    This model is used for the following user types:
    - librarian: School Librarian
    - counselor: School Counselor
    - accountant: School Accountant
    - secretary: School Secretary
    - nurse: School Nurse
    - maintenance: Maintenance Staff
    - security: Security Staff
    - driver: School Driver
    - staff: Legacy staff type
    """
    # Direct link to CustomUser
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, primary_key=True)

    # Role choices for staff members
    ROLE_CHOICES = [
        # Academic Support Staff
        ('librarian', 'Librarian'),
        ('counselor', 'School Counselor'),

        # Administrative Support Staff
        ('accountant', 'Accountant'),
        ('secretary', 'Secretary'),

        # Service Support Staff
        ('nurse', 'School Nurse'),
        ('maintenance', 'Maintenance Staff'),
        ('security', 'Security Staff'),
        ('driver', 'Driver'),

        # Other
        ('other', 'Other Staff')
    ]

    school_branch = models.ForeignKey('schools.SchoolBranch', on_delete=models.CASCADE, related_name='staff')
    staff_number = models.CharField(max_length=15, unique=True)
    department = models.ForeignKey('academics.Department', on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, help_text="Staff member's specific role")
    phone_number = models.CharField(
        max_length=15,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
            )
        ]
    )
    email = models.EmailField(unique=True)
    date_of_birth = models.DateField()
    gender = models.CharField(max_length=1, choices=SELECT_GENDER)
    profile_picture = models.ImageField(upload_to='staff_profile_pics/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta(UserProfileMixin.Meta): # Inherit Meta from mixin
        app_label = 'users'
        verbose_name = 'Staff Profile'
        verbose_name_plural = 'Staff Profiles'
        ordering = ['-date_of_birth']

    def save(self, *args, **kwargs):
        print("Staff save called") # Debug print
        # Generate staff_number only if it's a new profile and user is set (check self.pk)
        if not self.staff_number and self.pk:
            print("Generating Staff number") # Debug print
            try:
                self.staff_number = self.generate_unique_number('STF')
                print(f"Generated Staff number: {self.staff_number}") # Debug print
            except Exception as e:
                print(f"Error generating Staff number: {e}") # Debug print
                raise # Re-raise the exception
        super().save(*args, **kwargs)

    def __str__(self):
        # Access user's name directly via the 'user' OneToOneField
        if self.user:
            return f"{self.user.get_full_name()} ({self.staff_number or 'N/A'})"
        return f"Staff Profile ({self.staff_number or 'N/A'})"


class Parent(UserProfileMixin, models.Model):
    """Profile model for parents/guardians.

    This model is used for the 'parent' user type.
    """
    # Direct link to CustomUser
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, primary_key=True)

    school_branch = models.ManyToManyField(SchoolBranch, related_name='parents')
    phone_number = models.CharField(
        max_length=15,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
            )
        ]
    )
    email = models.EmailField(unique=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    gender = models.CharField(max_length=1, choices=SELECT_GENDER)
    profile_picture = models.ImageField(upload_to='parent_profile_pics/', blank=True, null=True)
    relationship = models.CharField(max_length=20, choices=SELECT_RELATIONSHIP)
    children = models.ManyToManyField('Student', related_name='parents')
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        related_name='parent_users',
        blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta(UserProfileMixin.Meta): # Inherit Meta from mixin
        app_label = 'users'
        verbose_name = 'Parent Profile'
        verbose_name_plural = 'Parent Profiles'
        ordering = ['-relationship']

    def save(self, *args, **kwargs):
        print("Parent save called") # Debug print
        # Parents don't have a generated unique number based on your model, so no number generation logic here.
        super().save(*args, **kwargs)

    def __str__(self):
        # Access user's name directly via the 'user' OneToOneField
        if self.user:
            return f"{self.user.get_full_name()} ({self.relationship or 'N/A'})"
        return f"Parent Profile ({self.relationship or 'N/A'})"


class Teacher(UserProfileMixin, models.Model):
    """Profile model for teachers.

    This model is used for the 'teacher' user type.
    """
    # Direct link to CustomUser
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, primary_key=True)

    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, blank=True, null=True, related_name='teachers')
    is_class_teacher = models.BooleanField(default=False)
    assigned_classes = models.ManyToManyField('academics.ClassRoom', related_name='teachers')
    subjects_taught = models.ForeignKey('academics.Subject', related_name='teaching_staff', blank=True, null=True, on_delete=models.CASCADE)
    teacher_number = models.CharField(max_length=15, unique=True)
    date_of_birth = models.DateField()
    national_id = models.CharField(
        max_length=20, 
        unique=True,
        validators=[
            RegexValidator(
                regex=r'^\d{8,20}$',
                message="National ID must contain 8-20 digits."
            )
        ]
    )
    phone_number = models.CharField(
        max_length=15,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
            )
        ]
    )
    email = models.EmailField(unique=True)
    personal_email = models.EmailField(max_length=100, blank=True, null=True)
    gender = models.CharField(max_length=1, choices=SELECT_GENDER)
    profile_picture = models.ImageField(upload_to='teacher_profile_pics/', blank=True, null=True)
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        related_name='teacher_users',
        blank=True
    )
    department_assignments = models.ManyToManyField(
        'academics.Department',
        through='academics.TeacherDepartmentAssignment',
        related_name='assigned_teachers'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta(UserProfileMixin.Meta): # Inherit Meta from mixin
        app_label = 'users'
        verbose_name = 'Teacher Profile'
        verbose_name_plural = 'Teacher Profiles'
        ordering = ['-date_of_birth']

    def save(self, *args, **kwargs):
        print("Teacher save called") # Debug print
        # Generate teacher_number only if it's a new profile and user is set (check self.pk)
        if not self.teacher_number and self.pk:
            print("Generating Teacher number") # Debug print
            try:
                self.teacher_number = self.generate_unique_number('TCH')
                print(f"Generated Teacher number: {self.teacher_number}") # Debug print
            except Exception as e:
                print(f"Error generating Teacher number: {e}") # Debug print
                raise # Re-raise the exception
        super().save(*args, **kwargs)

    def __str__(self):
        # Access user's name directly via the 'user' OneToOneField
        if self.user:
            return f"{self.user.get_full_name()} ({self.teacher_number or 'N/A'})"
        return f"Teacher Profile ({self.teacher_number or 'N/A'})"

    @property
    def departments(self):
        """Get all departments this teacher belongs to."""
        # Safely access user and its profile
        if self.user and hasattr(self.user, 'teacher_profile') and self.user.teacher_profile:
             # Access department_assignments directly from the teacher profile instance (self)
             return [assignment.department for assignment in self.department_assignments.all()]
        return []

    @property
    def is_head_of_department(self):
        """Check if this teacher is a head of any department."""
        # Safely access user and its profile
        if self.user and hasattr(self.user, 'teacher_profile') and self.user.teacher_profile:
            # Access department_assignments directly from the teacher profile instance (self)
            return self.department_assignments.filter(is_head=True).exists()
        return False

    def get_head_department(self):
        """Get the department where this teacher is the head."""
        # Safely access user and its profile
        if self.user and hasattr(self.user, 'teacher_profile') and self.user.teacher_profile:
            # Access department_assignments directly from the teacher profile instance (self)
            assignment = self.department_assignments.filter(is_head=True).first()
            return assignment.department if assignment else None
        return None


class Student(UserProfileMixin, models.Model):
    """Profile model for students.

    This model is used for the 'student' user type.
    """
    # Direct link to CustomUser
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, primary_key=True)

    date_of_birth = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=1, choices=SELECT_GENDER)
    birth_certificate = models.ImageField(upload_to='student_birth_certificates/', blank=True, null=True)
    result_slip = models.ImageField(upload_to='student_result_slips/', blank=True, null=True)
    admission_letter = models.ImageField(upload_to='student_admission_letters/', blank=True, null=True)
    school_branch = models.ForeignKey('schools.SchoolBranch', on_delete=models.CASCADE, related_name='students', blank=True, null=True)
    admission_date = models.DateField(auto_now=True)
    class_teacher = models.ForeignKey('users.Teacher', on_delete=models.CASCADE, related_name='class_students', blank=True, null=True)
    class_name = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE, related_name='enrolled_students', blank=True, null=True)
    stream = models.ForeignKey('academics.Stream', on_delete=models.CASCADE, blank=True, null=True)
    admission_number = models.CharField(max_length=25, unique=True, blank=True, null=True)
    current_class = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE, related_name='current_students', blank=True, null=True)
    subjects = models.ManyToManyField('academics.Subject', related_name='enrolled_students', blank=True)
    subject_type = models.CharField(max_length=100, choices=SELECT_SUBJECT_TYPE, blank=True, null=True)
    address = models.TextField(null=True, blank=True)
    phone_number = models.CharField(
        max_length=15, 
        null=True, 
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
            )
        ]
    )
    parent = models.ForeignKey('users.Parent', on_delete=models.SET_NULL, null=True, related_name='student_children', blank=True)
    emergency_contact = models.CharField(
        max_length=15, 
        null=True, 
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
            )
        ]
    )
    additional_notes = models.TextField(blank=True, null=True)

    # Guardian information
    guardian_name = models.CharField(max_length=100, blank=True, null=True, help_text="Full name of parent/guardian")
    guardian_contact = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
            )
        ],
        help_text="Primary contact number for guardian"
    )
    guardian_email = models.EmailField(blank=True, null=True, help_text="Guardian's email address")
    guardian_relationship = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        choices=[
            ('Father', 'Father'),
            ('Mother', 'Mother'),
            ('Guardian', 'Guardian'),
            ('Grandfather', 'Grandfather'),
            ('Grandmother', 'Grandmother'),
            ('Uncle', 'Uncle'),
            ('Aunt', 'Aunt'),
            ('Other', 'Other'),
        ],
        help_text="Relationship to student"
    )
    guardian_occupation = models.CharField(max_length=100, blank=True, null=True, help_text="Guardian's occupation")

    # Medical information
    blood_type = models.CharField(
        max_length=3,
        blank=True,
        null=True,
        choices=[
            ('A+', 'A+'),
            ('A-', 'A-'),
            ('B+', 'B+'),
            ('B-', 'B-'),
            ('AB+', 'AB+'),
            ('AB-', 'AB-'),
            ('O+', 'O+'),
            ('O-', 'O-'),
        ],
        help_text="Student's blood type"
    )
    medical_conditions = models.TextField(blank=True, null=True, help_text="Medical conditions, allergies, or special medical needs")

    # Additional personal details
    nationality = models.CharField(max_length=50, blank=True, null=True, help_text="Student's nationality")
    religion = models.CharField(max_length=50, blank=True, null=True, help_text="Student's religion")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta(UserProfileMixin.Meta): # Inherit Meta from mixin
        app_label = 'users'
        verbose_name = 'Student Profile'
        verbose_name_plural = 'Student Profiles'
        ordering = ['-admission_date']

    def save(self, *args, **kwargs):
        print("Student save called") # Debug print
        # Set created_at if it's not set
        if not self.created_at:
            self.created_at = timezone.now()
            
        # Generate admission_number only if it's a new profile and user is set (check self.pk)
        if not self.admission_number and self.pk:
            print("Generating Student number") # Debug print
            try:
                self.admission_number = self.generate_unique_number('STD')
                print(f"Generated Student number: {self.admission_number}") # Debug print
            except Exception as e:
                print(f"Error generating Student number: {e}") # Debug print
                raise # Re-raise the exception
        super().save(*args, **kwargs)

    def __str__(self):
        # Access user's name directly via the 'user' OneToOneField
        if self.user:
            return f"{self.user.get_full_name()} ({self.admission_number or 'N/A'})"
        return f"Student Profile ({self.admission_number or 'N/A'})"

    def get_absolute_url(self):
        """Get the URL for the student's detail view."""
        from django.urls import reverse
        # Safely access user's ID
        if self.user:
            return reverse('student-detail', args=[str(self.user.pk)]) # Use self.user.pk
        return '' # Return empty string or a default URL if user is not set

    def get_current_class_name(self):
        """Get the name of the student's current class."""
        return self.current_class.name if self.current_class else ''

    def get_stream_name(self):
        """Get the name of the student's stream."""
        return self.stream.name if self.stream else ''

    def get_subjects_list(self):
        """Get a list of subjects the student is enrolled in."""
        return ", ".join([subject.name for subject in self.subjects.all()])

    def get_parent_name(self):
        """Get the name of the student's parent."""
        # Safely access parent's get_full_name
        if self.parent:
            return self.parent.get_full_name()
        return ''


class ParentStaffLink(models.Model):
    """
    Model to link staff members (teachers, administrators, etc.) with their children who are students.
    This allows staff members to access their children's information without needing a separate parent account.
    """
    # The staff member (can be any staff type: teacher, admin, etc.)
    # Assuming staff_user here is a CustomUser instance
    staff_user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='staff_children_links',
                                  limit_choices_to={'user_type__in': ['teacher', 'school_admin', 'deputy_principal',
                                    'branch_admin', 'department_head', 'ict_admin',
                                    'librarian', 'counselor', 'accountant', 'secretary',
                                    'nurse', 'maintenance', 'security', 'driver', 'staff']})

    # The student who is the child of the staff member
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='staff_parent_links')

    # Relationship to the student
    relationship = models.CharField(max_length=20, choices=SELECT_RELATIONSHIP)

    # Whether this staff member is the primary contact for the student
    is_primary_contact = models.BooleanField(default=False)

    # Notes about the relationship
    notes = models.TextField(blank=True, null=True)

    class Meta:
        app_label = 'users'
        verbose_name = 'Parent-Staff Link'
        verbose_name_plural = 'Parent-Staff Links'
        # Ensure a staff member can't be linked to the same student multiple times
        unique_together = ('staff_user', 'student')

    def __str__(self):
        # Safely access staff_user and student's get_full_name
        staff_name = self.staff_user.get_full_name() if self.staff_user and hasattr(self.staff_user, 'get_full_name') else 'N/A Staff'
        student_name = self.student.user.get_full_name() if self.student and self.student.user and hasattr(self.student.user, 'get_full_name') else 'N/A Student'
        return f"{staff_name} - {student_name} ({self.relationship})"
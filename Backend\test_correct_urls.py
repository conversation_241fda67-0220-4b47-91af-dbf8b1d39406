#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from core.models import CustomUser

def test_correct_urls():
    print("🔍 Testing Correct API URLs...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    headers = {'Authorization': f'Bearer {access_token}'}
    
    # Test the correct URLs that the frontend should be calling
    test_urls = [
        'http://localhost:8000/api/schools/schools/',
        'http://localhost:8000/api/schools/branch/',
    ]
    
    for url in test_urls:
        print(f"\n🔍 Testing: {url}")
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"   📡 Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                results_count = len(data.get('results', []))
                print(f"   ✅ Success: {results_count} items found")
                
                if results_count > 0:
                    sample_item = data['results'][0]
                    if 'schools' in url:
                        print(f"   📋 Sample school: {sample_item.get('name')} (ID: {sample_item.get('id')})")
                    else:
                        print(f"   📋 Sample branch: {sample_item.get('name')} (ID: {sample_item.get('id')})")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                print(f"   Error: {response.text[:200]}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Request failed: {str(e)}")
    
    # Test the incorrect URLs that were causing 404s
    print(f"\n🔍 Testing incorrect URLs (should fail with 404)...")
    incorrect_urls = [
        'http://localhost:8000/schools/schools/',
        'http://localhost:8000/schools/branch/',
    ]
    
    for url in incorrect_urls:
        print(f"\n🔍 Testing: {url}")
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"   📡 Status: {response.status_code}")
            
            if response.status_code == 404:
                print(f"   ✅ Expected 404 - URL is correctly invalid")
            else:
                print(f"   ⚠️ Unexpected status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Request failed: {str(e)}")
    
    print(f"\n🎯 URL test complete!")
    print(f"📋 Frontend should now work with the corrected /api/ prefix!")

if __name__ == "__main__":
    test_correct_urls()

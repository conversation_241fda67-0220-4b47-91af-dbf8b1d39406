#!/usr/bin/env python
import os
import sys
import django
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from schools.models import School, SchoolBranch
from users.models import Student

def test_students_api():
    print("🧪 Testing Students API...")
    
    # Get all schools and branches
    schools = School.objects.all()
    
    print(f"\n📋 Current Students in Database:")
    for school in schools:
        branches = SchoolBranch.objects.filter(school=school)
        for branch in branches:
            students = Student.objects.filter(school_branch=branch)
            print(f"   🏫 {school.name} - {branch.name} (ID: {branch.id}): {students.count()} students")
            for student in students:
                print(f"      👨‍🎓 {student.user.first_name} {student.user.last_name} (ID: {student.user.id})")
    
    base_url = "http://localhost:8000/api/users/students/"
    
    # Test different combinations
    test_cases = [
        {"name": "No filters (all students)", "params": {}},
        {"name": "Sample School - Main Campus", "params": {"school_branch": 5}},
        {"name": "MAKINI SCHOOL - Main", "params": {"school_branch": 2}},
        {"name": "ShuleXcel Academy - Main", "params": {"school_branch": 1}},
    ]
    
    for test_case in test_cases:
        print(f"\n🔍 Testing: {test_case['name']}")
        print(f"   Parameters: {test_case['params']}")
        
        try:
            response = requests.get(base_url, params=test_case['params'], timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ SUCCESS: {response.status_code}")
                print(f"   📊 Results:")
                print(f"      - Count: {data.get('count', 0)}")
                print(f"      - Students returned: {len(data.get('results', []))}")
                
                # Show first few students
                students = data.get('results', [])
                for i, student in enumerate(students[:3]):
                    name = f"{student.get('first_name', '')} {student.get('last_name', '')}"
                    admission = student.get('admission_number', 'N/A')
                    print(f"      - Student {i+1}: {name} ({admission})")
                    
            elif response.status_code == 401:
                print(f"   🔐 AUTHENTICATION REQUIRED: {response.status_code}")
            else:
                print(f"   ❌ ERROR: {response.status_code} - {response.text[:200]}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ CONNECTION ERROR: {str(e)}")

if __name__ == "__main__":
    test_students_api()

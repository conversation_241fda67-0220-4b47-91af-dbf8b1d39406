#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from core.models import CustomUser
from schools.models import School, SchoolBranch
from users.models import Student, Teacher, Staff

def check_current_user():
    print("🔍 Checking current user profiles...")
    
    # Get Sample School and Main Campus
    try:
        sample_school = School.objects.get(name="Sample School")
        main_campus = SchoolBranch.objects.get(school=sample_school, name="Main Campus")
        print(f"✅ Target School: {sample_school.name}")
        print(f"✅ Target Branch: {main_campus.name}")
    except (School.DoesNotExist, SchoolBranch.DoesNotExist):
        print("❌ Sample School or Main Campus not found!")
        return
    
    # Check all users and their profiles
    print(f"\n=== ALL USERS AND THEIR PROFILES ===")
    users = CustomUser.objects.all()
    
    for user in users:
        print(f"\n👤 User: {user.username} ({user.email})")
        print(f"   - Is Staff: {user.is_staff}")
        print(f"   - Is Superuser: {user.is_superuser}")
        print(f"   - Is Active: {user.is_active}")
        
        # Check for profiles
        has_profile = False
        
        try:
            if hasattr(user, 'student'):
                student = user.student
                print(f"   - 👨‍🎓 Student Profile: {student.school_branch}")
                has_profile = True
        except:
            pass
            
        try:
            if hasattr(user, 'teacher'):
                teacher = user.teacher
                print(f"   - 👨‍🏫 Teacher Profile: {teacher.school_branch}")
                has_profile = True
        except:
            pass
            
        try:
            if hasattr(user, 'staff'):
                staff = user.staff
                print(f"   - 👨‍💼 Staff Profile: {staff.school_branch}")
                has_profile = True
        except:
            pass
        
        if not has_profile:
            print(f"   - ❌ NO PROFILE FOUND")
            
            # Create a staff profile for admin users
            if user.is_staff or user.is_superuser:
                print(f"   - 🔧 Creating staff profile for admin user...")
                try:
                    staff_profile = Staff.objects.create(
                        user=user,
                        school_branch=main_campus,
                        employee_id=f"STAFF{user.id:04d}",
                        department="Administration",
                        position="Administrator"
                    )
                    print(f"   - ✅ Created staff profile: {staff_profile}")
                except Exception as e:
                    print(f"   - ❌ Failed to create staff profile: {e}")

if __name__ == "__main__":
    check_current_user()

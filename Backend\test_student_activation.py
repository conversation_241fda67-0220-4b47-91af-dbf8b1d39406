#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_student_activation():
    print("🔍 Testing Student Activation/Deactivation...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    
    # Get a student for testing
    student = Student.objects.first()
    if not student:
        print("❌ No students found for testing")
        return
    
    print(f"\n📋 Testing with student: {student.user.first_name} {student.user.last_name}")
    print(f"   📋 Student ID: {student.user.id}")
    print(f"   📋 Current status: {'Active' if student.user.is_active else 'Inactive'}")
    
    # Test 1: Deactivate student
    print(f"\n🔍 Test 1: Deactivating student...")
    deactivate_url = f'http://localhost:8000/api/users/students/{student.user.id}/'
    deactivate_data = {'is_active': False}
    
    try:
        deactivate_response = requests.patch(deactivate_url, json=deactivate_data, headers=headers, timeout=10)
        print(f"   📡 Deactivate API Response: {deactivate_response.status_code}")
        
        if deactivate_response.status_code == 200:
            print(f"   ✅ Deactivation successful!")
            
            # Verify in database
            student.user.refresh_from_db()
            print(f"   📋 Database status: {'Active' if student.user.is_active else 'Inactive'}")
            
            if not student.user.is_active:
                print(f"   ✅ Database confirms deactivation!")
            else:
                print(f"   ❌ Database still shows active")
        else:
            print(f"   ❌ Deactivation failed: {deactivate_response.status_code}")
            print(f"   Error: {deactivate_response.text[:200]}")
            return
    except Exception as e:
        print(f"   ❌ Deactivation request failed: {str(e)}")
        return
    
    # Test 2: Reactivate student
    print(f"\n🔍 Test 2: Reactivating student...")
    activate_data = {'is_active': True}
    
    try:
        activate_response = requests.patch(deactivate_url, json=activate_data, headers=headers, timeout=10)
        print(f"   📡 Activate API Response: {activate_response.status_code}")
        
        if activate_response.status_code == 200:
            print(f"   ✅ Activation successful!")
            
            # Verify in database
            student.user.refresh_from_db()
            print(f"   📋 Database status: {'Active' if student.user.is_active else 'Inactive'}")
            
            if student.user.is_active:
                print(f"   ✅ Database confirms activation!")
            else:
                print(f"   ❌ Database still shows inactive")
        else:
            print(f"   ❌ Activation failed: {activate_response.status_code}")
            print(f"   Error: {activate_response.text[:200]}")
            return
    except Exception as e:
        print(f"   ❌ Activation request failed: {str(e)}")
        return
    
    # Test 3: Check filtered lists
    print(f"\n🔍 Test 3: Testing filtered student lists...")
    
    # Test active students list
    try:
        active_url = 'http://localhost:8000/api/users/students/?is_active=true'
        active_response = requests.get(active_url, headers=headers, timeout=10)
        
        if active_response.status_code == 200:
            active_data = active_response.json()
            active_count = len(active_data.get('results', []))
            print(f"   ✅ Active students API: {active_count} students")
        else:
            print(f"   ❌ Active students API failed: {active_response.status_code}")
    except Exception as e:
        print(f"   ❌ Active students request failed: {str(e)}")
    
    # Test inactive students list
    try:
        inactive_url = 'http://localhost:8000/api/users/students/?is_active=false'
        inactive_response = requests.get(inactive_url, headers=headers, timeout=10)
        
        if inactive_response.status_code == 200:
            inactive_data = inactive_response.json()
            inactive_count = len(inactive_data.get('results', []))
            print(f"   ✅ Inactive students API: {inactive_count} students")
        else:
            print(f"   ❌ Inactive students API failed: {inactive_response.status_code}")
    except Exception as e:
        print(f"   ❌ Inactive students request failed: {str(e)}")
    
    print(f"\n🎯 Student activation test complete!")
    print(f"\n📊 Summary:")
    print(f"   - Deactivation API: {'✅' if deactivate_response.status_code == 200 else '❌'}")
    print(f"   - Activation API: {'✅' if activate_response.status_code == 200 else '❌'}")
    print(f"   - Database updates: {'✅' if student.user.is_active else '❌'}")
    print(f"   - Filtered lists: {'✅' if active_response.status_code == 200 and inactive_response.status_code == 200 else '❌'}")
    
    print(f"\n🎉 Student activation/deactivation functionality is working!")

if __name__ == "__main__":
    test_student_activation()

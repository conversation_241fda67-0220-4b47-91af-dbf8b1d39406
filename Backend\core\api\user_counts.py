from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Count
from core.models import CustomUser
from schools.models import School, SchoolBranch
from users.models import Student, Teacher, Staff, AdminProfile, Parent

class UserCountsView(APIView):
    """
    API view to get user counts filtered by school and branch
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        Get user counts filtered by school and/or branch
        
        Query Parameters:
        - school: School ID to filter by
        - branch: Branch ID to filter by
        """
        try:
            # Get filter parameters
            school_id = request.query_params.get('school')
            branch_id = request.query_params.get('branch')
            
            print(f"🔍 UserCounts API called with school_id={school_id}, branch_id={branch_id}")
            
            # Initialize counts
            counts = {
                'totalStudents': 0,
                'totalTeachers': 0,
                'totalStaff': 0,
                'totalAdmins': 0,
                'totalParents': 0,
                'total': 0
            }
            
            # If no filters provided, return global counts
            if not school_id and not branch_id:
                counts['totalStudents'] = Student.objects.count()
                counts['totalTeachers'] = Teacher.objects.count()
                counts['totalStaff'] = Staff.objects.count()
                counts['totalAdmins'] = AdminProfile.objects.count()
                counts['totalParents'] = Parent.objects.count()
                counts['total'] = CustomUser.objects.count()
                print(f"📊 Global counts: {counts}")
                return Response(counts)
            
            # Validate school_id if provided
            school = None
            if school_id:
                try:
                    school = School.objects.get(id=school_id)
                    print(f"✅ Found school: {school.name}")
                except School.DoesNotExist:
                    return Response(
                        {"error": f"School with ID {school_id} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
                except ValueError:
                    return Response(
                        {"error": "Invalid school ID format"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Validate branch_id if provided
            branch = None
            if branch_id:
                try:
                    branch = SchoolBranch.objects.get(id=branch_id)
                    print(f"✅ Found branch: {branch.name}")
                    # If school is also provided, ensure branch belongs to that school
                    if school and branch.school != school:
                        return Response(
                            {"error": f"Branch {branch.name} does not belong to school {school.name}"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                except SchoolBranch.DoesNotExist:
                    return Response(
                        {"error": f"Branch with ID {branch_id} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
                except ValueError:
                    return Response(
                        {"error": "Invalid branch ID format"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Apply filters and count
            if branch_id:
                # Filter by specific branch
                counts['totalStudents'] = Student.objects.filter(school_branch_id=branch_id).count()
                counts['totalTeachers'] = Teacher.objects.filter(school_branch_id=branch_id).count()
                counts['totalStaff'] = Staff.objects.filter(school_branch_id=branch_id).count()
                counts['totalAdmins'] = AdminProfile.objects.filter(school_branch_id=branch_id).count()
                # Parents can be associated with multiple branches, so we need to use ManyToMany
                counts['totalParents'] = Parent.objects.filter(school_branch__id=branch_id).distinct().count()
                
            elif school_id:
                # Filter by school (all branches in the school)
                school_branches = SchoolBranch.objects.filter(school_id=school_id)
                branch_ids = list(school_branches.values_list('id', flat=True))
                
                counts['totalStudents'] = Student.objects.filter(school_branch_id__in=branch_ids).count()
                counts['totalTeachers'] = Teacher.objects.filter(school_branch_id__in=branch_ids).count()
                counts['totalStaff'] = Staff.objects.filter(school_branch_id__in=branch_ids).count()
                counts['totalAdmins'] = AdminProfile.objects.filter(school_branch_id__in=branch_ids).count()
                counts['totalParents'] = Parent.objects.filter(school_branch__id__in=branch_ids).distinct().count()
            
            # Calculate total
            counts['total'] = (
                counts['totalStudents'] + 
                counts['totalTeachers'] + 
                counts['totalStaff'] + 
                counts['totalAdmins'] + 
                counts['totalParents']
            )
            
            print(f"📊 Filtered counts: {counts}")
            return Response(counts, status=status.HTTP_200_OK)
            
        except Exception as e:
            print(f"❌ Error in UserCounts API: {str(e)}")
            return Response(
                {"error": f"Error getting user counts: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

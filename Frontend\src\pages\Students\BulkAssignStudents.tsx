import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";

import PageBreadcrumb from "../../components/common/PageBreadcrumb";
import PageMeta from "../../components/common/PageMeta";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "../../components/ui/table";
import Button from "../../components/ui/button/Button";
import Select from "../../components/form/Select";
import Label from "../../components/form/Label";

import { studentService, type Student } from "../../services/studentService";
import axiosInstance from "../../components/utils/AxiosInstanceFixed";
import { useSchool } from "../../contexts/SchoolContext";

interface BulkAssignmentData {
  school_branch_id: number;
  students: number[];
}

export default function BulkAssignStudents() {
  const navigate = useNavigate();
  const location = useLocation();
  const studentIds = location.state?.studentIds || [];

  const [students, setStudents] = useState<Student[]>([]);
  const [schools, setSchools] = useState<any[]>([]);
  const [branches, setBranches] = useState<any[]>([]);
  const [selectedSchoolForBranches, setSelectedSchoolForBranches] = useState<number | null>(null);
  const [selectedSchoolBranchId, setSelectedSchoolBranchId] = useState<number | null>(null);

  // Get school context
  const { selectedSchool } = useSchool();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (studentIds.length === 0) {
      setError('No students selected for assignment');
      setLoading(false);
      return;
    }

    fetchData();
  }, [studentIds]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch selected students
      const studentPromises = studentIds.map((id: number) => 
        studentService.getStudentById(id)
      );
      const studentsData = await Promise.all(studentPromises);
      setStudents(studentsData);

      // Fetch schools and branches using axios with proper authentication
      const schoolsResponse = await axiosInstance.get('/api/schools/schools/');
      setSchools(schoolsResponse.data.results || []);

      // Determine which school to use for branch filtering
      let schoolIdForBranches = selectedSchoolForBranches;

      // If no school selected, try to use current user's selected school from context
      if (!schoolIdForBranches && selectedSchool?.id) {
        schoolIdForBranches = selectedSchool.id;
      } else if (!schoolIdForBranches && schoolsResponse.data.results?.length > 0) {
        // Default to first school if no context available
        schoolIdForBranches = schoolsResponse.data.results[0].id;
      }

      if (schoolIdForBranches) {
        // Fetch branches for the specific school
        const branchesResponse = await axiosInstance.get(`/api/schools/branch/?school=${schoolIdForBranches}`);
        setBranches(branchesResponse.data.results || []);
        setSelectedSchoolForBranches(schoolIdForBranches);
        console.log(`✅ Branches loaded for school ${schoolIdForBranches}:`, branchesResponse.data.results?.length || 0);
      } else {
        // Fallback: fetch all branches
        const branchesResponse = await axiosInstance.get('/api/schools/branch/');
        setBranches(branchesResponse.data.results || []);
        console.log('✅ All branches loaded (no school filter)');
      }

    } catch (err: unknown) {
      const error = err as { response?: { data?: { message?: string } }, message?: string };
      console.error('Error fetching data:', error);
      setError(error.response?.data?.message || "Failed to fetch data");
    } finally {
      setLoading(false);
    }
  };

  const handleBulkAssign = async () => {
    if (!selectedSchoolBranchId) {
      alert('Please select a school branch first');
      return;
    }

    if (students.length === 0) {
      alert('No students to assign');
      return;
    }

    const confirmMessage = `🔄 Bulk Assignment Confirmation\n\nAssign ${students.length} students to the selected school branch?\n\nThis will update their school and branch assignments.`;
    
    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      setSaving(true);
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (const student of students) {
        try {
          // Update student with new school branch assignment
          await studentService.updateStudent(student.id, {
            school_branch_id: selectedSchoolBranchId
          });
          successCount++;
        } catch (error: any) {
          console.error(`Failed to assign student ${student.id}:`, error);
          errorCount++;
          errors.push(`${student.first_name} ${student.last_name}: ${error.response?.data?.message || error.message || 'Unknown error'}`);
        }
      }

      if (errorCount === 0) {
        alert(`✅ Success!\n\nAssigned ${successCount} students successfully.`);
        navigate('/unassigned-students');
      } else {
        const errorMessage = `⚠️ Partial Success\n\nAssigned: ${successCount} students\nFailed: ${errorCount} students\n\nErrors:\n${errors.slice(0, 3).join('\n')}${errors.length > 3 ? '\n...' : ''}`;
        alert(errorMessage);
      }

    } catch (error: any) {
      console.error('Bulk assignment error:', error);
      alert('❌ Failed to assign students. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const getIssueType = (student: Student): string => {
    const hasSchool = student.school_branch?.school?.id;
    const hasBranch = student.school_branch?.id;

    if (!hasSchool && !hasBranch) return 'No School & No Branch';
    if (!hasSchool) return 'No School';
    if (!hasBranch) return 'No Branch';
    return 'Assigned';
  };

  const getIssueColor = (student: Student): string => {
    const issueType = getIssueType(student);
    switch (issueType) {
      case 'No School & No Branch': return 'bg-red-100 text-red-800 border-red-200';
      case 'No School': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'No Branch': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
          <p className="mt-2">Loading students...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageMeta title="Bulk Assign Students" description="Assign multiple students to school branches" />
      
      <PageBreadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "Students", href: "/students" },
          { label: "Unassigned Students", href: "/unassigned-students" },
          { label: "Bulk Assignment" }
        ]} 
      />

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Bulk Assign Students
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Assign {students.length} selected students to a school branch
              </p>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => navigate('/unassigned-students')}
              >
                ← Back to Unassigned
              </Button>
            </div>
          </div>
        </div>

        {error ? (
          <div className="p-6">
            <div className="text-center py-8">
              <div className="text-red-600 mb-4">
                <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-lg font-medium">Error Loading Data</p>
                <p className="text-sm">{error}</p>
              </div>
              <Button onClick={fetchData}>
                Try Again
              </Button>
            </div>
          </div>
        ) : (
          <>
            {/* Assignment Form */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
                <h3 className="font-medium text-gray-700 dark:text-white/80 mb-4 text-lg">
                  🎯 Assignment Configuration
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <Label>Select School <span className="text-blue-500">*</span></Label>
                    <Select
                      options={[
                        { value: "", label: "Choose School..." },
                        ...schools.map(school => ({
                          value: school.id.toString(),
                          label: school.name
                        }))
                      ]}
                      value={selectedSchoolForBranches?.toString() || ""}
                      onChange={async (value) => {
                        const schoolId = value ? parseInt(value) : null;
                        if (schoolId) {
                          try {
                            const branchesResponse = await axiosInstance.get(`/api/schools/branch/?school=${schoolId}`);
                            setBranches(branchesResponse.data.results || []);
                            setSelectedSchoolForBranches(schoolId);
                            setSelectedSchoolBranchId(null); // Reset branch selection
                          } catch (error) {
                            console.error('Error fetching branches for school:', error);
                          }
                        }
                      }}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Select school to filter available branches
                    </p>
                  </div>

                  <div>
                    <Label>Select School Branch <span className="text-red-500">*</span></Label>
                    <Select
                      options={[
                        { value: "", label: branches.length === 0 ? "Select school first" : "Choose a school branch..." },
                        ...branches.map(branch => {
                          // Find school name from schools array
                          const school = schools.find(s => s.id === branch.school);
                          const schoolName = school?.name || branch.school_name || 'Unknown School';
                          return {
                            value: branch.id.toString(),
                            label: `${branch.name} (${schoolName})`
                          };
                        })
                      ]}
                      value={selectedSchoolBranchId?.toString() || ""}
                      onChange={(value) => setSelectedSchoolBranchId(value ? parseInt(value) : null)}
                      disabled={branches.length === 0}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {branches.length === 0
                        ? 'No branches available for selected school'
                        : `${branches.length} branches available`
                      }
                    </p>
                  </div>

                  <div>
                    <Label>Assignment Summary</Label>
                    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                      <div className="flex items-center gap-2 text-blue-600 mb-2">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-sm font-medium">{students.length} Students Selected</span>
                      </div>
                      <p className="text-xs text-gray-500">
                        {selectedSchoolBranchId 
                          ? 'Ready to assign to selected branch' 
                          : 'Please select a school branch to proceed'
                        }
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end">
                  <Button
                    onClick={handleBulkAssign}
                    disabled={!selectedSchoolBranchId || saving}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {saving ? (
                      <>
                        <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-r-transparent mr-2"></div>
                        Assigning...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Assign {students.length} Students
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>

            {/* Students List */}
            <div className="p-6">
              <h3 className="font-medium text-gray-700 dark:text-white/80 mb-4 text-lg">
                📋 Students to be Assigned
              </h3>
              
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableCell isHeader>Student</TableCell>
                      <TableCell isHeader>Contact</TableCell>
                      <TableCell isHeader>Current Assignment</TableCell>
                      <TableCell isHeader>Issue</TableCell>
                      <TableCell isHeader>Status</TableCell>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {students.map((student) => (
                      <TableRow key={student.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium text-gray-900 dark:text-white">
                              {student.first_name} {student.last_name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {student.admission_number || 'No admission number'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="text-sm text-gray-900">
                              {student.user?.email || 'No email'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {student.phone_number || 'No phone'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="text-sm">
                              <span className="font-medium">School:</span> {student.school_branch?.school?.name || 'Not assigned'}
                            </div>
                            <div className="text-sm">
                              <span className="font-medium">Branch:</span> {student.school_branch?.name || 'Not assigned'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getIssueColor(student)}`}>
                            {getIssueType(student)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            student.user?.is_active 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {student.user?.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from django.db.models import Count

from .permissions import IsTeacherOrAdmin, IsStudentOrParent
from .serializers import (
    PerformanceAnalyticsSerializer,
    LearningPathSerializer,
    InterventionAnalyticsSerializer,
    TeacherPerformanceMetricsSerializer
)
from ..models import (
    Student, Subject, Term, Assessment, 
    LearningPath, SmartIntervention, PerformanceAnalytics,
    Department, TeacherPerformanceMetrics
)
from ..services.teacher_performance import TeacherPerformanceService
from users.models import Teacher

class AcademicAnalyticsViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated, IsTeacherOrAdmin]

    def list(self, request):
        """Handle GET requests to /api/analytics/"""
        return Response({
            'student_performance': 'View student performance analytics',
            'learning_paths': 'Generate and manage learning paths',
            'performance_tracking': 'Track student progress',
            'dashboard': 'View dashboard metrics'
        })

    @action(detail=True, methods=['get'])
    def student_performance(self, request, pk=None):
        student = get_object_or_404(Student, pk=pk)
        term = Term.objects.filter(is_current=True).first()
        
        analytics = PerformanceAnalytics.objects.filter(
            student=student,
            term=term
        ).select_related('subject')
        
        serializer = PerformanceAnalyticsSerializer(analytics, many=True)
        
        return Response({
            'analytics': serializer.data,
            'summary': self._generate_summary(analytics),
            'recommendations': self._generate_recommendations(analytics)
        })

    def _generate_summary(self, analytics):
        if not analytics:
            return {}
        
        return {
            'average_improvement': sum(a.improvement_percentage for a in analytics) / len(analytics),
            'subjects_improved': sum(1 for a in analytics if a.value_addition > 0),
            'subjects_needing_attention': sum(1 for a in analytics if a.intervention_needed)
        }

    def _generate_recommendations(self, analytics):
        recommendations = []
        for analytic in analytics:
            if analytic.intervention_needed:
                recommendations.append({
                    'subject': analytic.subject.name,
                    'reason': 'Intervention needed',
                    'suggestions': [
                        'Schedule remedial classes',
                        'Increase practice exercises',
                        'One-on-one tutoring'
                    ]
                })
        return recommendations

class LearningPathViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated, IsTeacherOrAdmin]
    
    @action(detail=True, methods=['post'])
    def generate_path(self, request, pk=None):
        student = get_object_or_404(Student, pk=pk)
        subject = get_object_or_404(Subject, pk=request.data.get('subject_id'))
        
        # Create or update learning path
        path, created = LearningPath.objects.get_or_create(
            student=student,
            subject=subject,
            defaults={
                'current_level': 'BEGINNER',
                'target_level': request.data.get('target_level', 'INTERMEDIATE'),
                'milestones': self._generate_milestones(subject)
            }
        )
        
        serializer = LearningPathSerializer(path)
        return Response(serializer.data, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)

    def _generate_milestones(self, subject):
        return [
            {
                'name': f'Milestone {i+1}',
                'description': f'Complete {subject.name} module {i+1}',
                'completed': False
            }
            for i in range(5)  # Example: 5 milestones per subject
        ]

class PerformanceTrackingViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated, IsStudentOrParent]
    
    @action(detail=True, methods=['get'])
    def progress_metrics(self, request, pk=None):
        student = get_object_or_404(Student, pk=pk)
        term = Term.objects.filter(is_current=True).first()
        
        analytics = PerformanceAnalytics.objects.filter(
            student=student,
            term=term
        ).select_related('subject')
        
        serializer = PerformanceAnalyticsSerializer(analytics, many=True)
        
        return Response({
            'metrics': serializer.data,
            'summary': self._generate_progress_summary(analytics)
        })

    def _generate_progress_summary(self, analytics):
        if not analytics:
            return {}
            
        return {
            'overall_progress': sum(a.improvement_percentage for a in analytics) / len(analytics),
            'subjects_improved': sum(1 for a in analytics if a.value_addition > 0),
            'areas_needing_attention': sum(1 for a in analytics if a.intervention_needed)
        }

class DashboardMetricsViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    
    def get_user_school_branch(self, user):
        """Helper method to get school branch from any user type"""
        print(f"Checking profiles for user {user.id} ({user.username}):")

        # Check for Staff profile
        try:
            if hasattr(user, 'staff'):
                staff_profile = user.staff
                print(f"Staff profile found: {staff_profile.school_branch}")
                return staff_profile.school_branch
        except:
            pass

        # Check for Teacher profile
        try:
            if hasattr(user, 'teacher'):
                teacher_profile = user.teacher
                print(f"Teacher profile found: {teacher_profile.school_branch}")
                return teacher_profile.school_branch
        except:
            pass

        # Check for Student profile
        try:
            if hasattr(user, 'student'):
                student_profile = user.student
                print(f"Student profile found: {student_profile.school_branch}")
                return student_profile.school_branch
        except:
            pass

        # Special handling for admin/staff users
        if user.is_staff or user.is_superuser:
            print("User is staff/superuser, using default branch")
            from schools.models import SchoolBranch
            # Get the first available school branch for admin users
            branch = SchoolBranch.objects.first()
            print(f"Default branch for admin: {branch}")
            return branch

        print("No school branch found for user")
        return None

    @action(detail=False, methods=['get'])
    def metrics(self, request):
        try:
            school_branch = self.get_user_school_branch(request.user)
            
            if not school_branch:
                return Response(
                    {'error': 'No school branch found'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            now = timezone.now()
            current_month = now.replace(day=1)
            previous_month = current_month - relativedelta(months=1)

            # Get current metrics with proper filtering
            current_metrics = {
                'students': Student.objects.filter(
                    school_branch=school_branch
                ).count(),
                'teachers': Teacher.objects.filter(
                    school_branch=school_branch
                ).count(),
                'subjects': Subject.objects.filter(
                    school_branch=school_branch
                ).count(),
                'departments': Department.objects.filter(
                    school_branch=school_branch
                ).count(),
            }

            # Get previous month metrics for comparison
            previous_metrics = {
                'students': Student.objects.filter(
                    school_branch=school_branch,
                    user__date_joined__lt=current_month
                ).count(),
                'teachers': Teacher.objects.filter(
                    school_branch=school_branch,
                    user__date_joined__lt=current_month
                ).count(),
            }

            # Calculate percentage changes
            metrics_data = {}
            for key in current_metrics:
                prev_value = previous_metrics.get(key, 0)
                curr_value = current_metrics[key]
                percent_change = (
                    ((curr_value - prev_value) / prev_value * 100)
                    if prev_value > 0 else 0
                )
                
                metrics_data[key] = {
                    'value': curr_value,
                    'change': round(percent_change, 1),
                    'trend': 'up' if percent_change > 0 else 'down'
                }

            return Response(metrics_data)

        except Exception as e:
            print(f"Error calculating metrics: {str(e)}")
            return Response(
                {'error': 'Error calculating metrics', 'detail': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class TeacherPerformanceViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated, IsTeacherOrAdmin]
    serializer_class = TeacherPerformanceMetricsSerializer

    def get_queryset(self):
        """Get performance metrics for the authenticated teacher"""
        if self.request.user.is_staff:
            return TeacherPerformanceMetrics.objects.all()
        return TeacherPerformanceMetrics.objects.filter(teacher=self.request.user)

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get a summary of teacher's performance"""
        term_id = request.query_params.get('term')
        if not term_id:
            return Response(
                {'error': 'Term ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        term = get_object_or_404(Term, id=term_id)
        service = TeacherPerformanceService(request.user)
        summary = service.get_performance_summary(term)

        if not summary:
            return Response(
                {'error': 'No performance data available'},
                status=status.HTTP_404_NOT_FOUND
            )

        return Response(summary)

    @action(detail=False, methods=['post'])
    def update_metrics(self, request):
        """Update performance metrics for the current term"""
        term_id = request.data.get('term')
        if not term_id:
            return Response(
                {'error': 'Term ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        term = get_object_or_404(Term, id=term_id)
        service = TeacherPerformanceService(request.user)
        metrics = service.update_all_metrics(term)

        serializer = self.get_serializer(metrics, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def department_performance(self, request):
        """Get performance metrics for all teachers in a department"""
        department_id = request.query_params.get('department')
        term_id = request.query_params.get('term')

        if not department_id or not term_id:
            return Response(
                {'error': 'Department ID and Term ID are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        term = get_object_or_404(Term, id=term_id)
        department = get_object_or_404(Department, id=department_id)

        metrics = TeacherPerformanceMetrics.objects.filter(
            teacher__department=department,
            term=term
        ).select_related('teacher', 'subject')

        serializer = self.get_serializer(metrics, many=True)
        return Response(serializer.data)

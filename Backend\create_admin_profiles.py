#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from core.models import CustomUser
from schools.models import School, SchoolBranch
from users.models import Staff
from academics.models import Department

def create_admin_profiles():
    print("🔧 Creating admin user profiles...")
    
    # Get Sample School and Main Campus
    try:
        sample_school = School.objects.get(name="Sample School")
        main_campus = SchoolBranch.objects.get(school=sample_school, name="Main Campus")
        print(f"✅ Target School: {sample_school.name}")
        print(f"✅ Target Branch: {main_campus.name}")
    except (School.DoesNotExist, SchoolBranch.DoesNotExist):
        print("❌ Sample School or Main Campus not found!")
        return
    
    # Get or create an Administration department
    admin_dept, created = Department.objects.get_or_create(
        name="Administration",
        school=sample_school,
        school_branch=main_campus,
        defaults={
            'code': 'ADMIN',
            'description': 'School Administration Department',
            'head_of_department': None
        }
    )
    if created:
        print(f"✅ Created Administration department: {admin_dept}")
    else:
        print(f"✅ Found existing Administration department: {admin_dept}")
    
    # Find admin users without profiles
    admin_users = CustomUser.objects.filter(
        is_staff=True,
        is_active=True
    )
    
    for user in admin_users:
        print(f"\n👤 Processing admin user: {user.username} ({user.email})")
        
        # Check if user already has a staff profile
        try:
            if hasattr(user, 'staff') and user.staff:
                print(f"   - ✅ Already has staff profile: {user.staff.school_branch}")
                continue
        except:
            pass
        
        # Create staff profile
        try:
            staff_profile = Staff.objects.create(
                user=user,
                school_branch=main_campus,
                employee_id=f"ADMIN{user.id:04d}",
                department=admin_dept,
                position="Administrator",
                hire_date="2024-01-01"
            )
            print(f"   - ✅ Created staff profile: {staff_profile}")
            print(f"   - 🏢 School Branch: {staff_profile.school_branch}")
            print(f"   - 🏛️ Department: {staff_profile.department}")
            print(f"   - 💼 Position: {staff_profile.position}")
            
        except Exception as e:
            print(f"   - ❌ Failed to create staff profile: {e}")
    
    print(f"\n=== VERIFICATION ===")
    # Verify all admin users now have profiles
    for user in admin_users:
        try:
            if hasattr(user, 'staff') and user.staff:
                print(f"✅ {user.username}: {user.staff.school_branch}")
            else:
                print(f"❌ {user.username}: No profile")
        except:
            print(f"❌ {user.username}: No profile")

if __name__ == "__main__":
    create_admin_profiles()

// AxiosInstanceFixed.tsx
import axios from 'axios';
import { API_BASE_URL } from '../../config/constants';

const axiosInstance = axios.create({
  baseURL: API_BASE_URL, // No /api prefix here
  headers: {
    'Content-Type': 'application/json',
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    // Try both new and old token keys for backward compatibility
    const token = localStorage.getItem('token') || localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle logout requests
    if (originalRequest.url && originalRequest.url.includes('/api/auth/logout')) {
      // Remove both old and new auth-related items
      localStorage.removeItem('token');
      localStorage.removeItem('access_token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
      localStorage.removeItem('user_data');
      delete axiosInstance.defaults.headers.common['Authorization'];
      return Promise.resolve({ status: 200 });
    }

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        const refresh = localStorage.getItem('refreshToken') || localStorage.getItem('refresh_token');
        // Use the correct token refresh endpoint
        const response = await axios.post(
          `${API_BASE_URL}/api/core/auth/token/refresh/`,
          { refresh }
        );

        const { access } = response.data;
        // Store token as 'token' for consistency with authService
        localStorage.setItem('token', access);
        axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${access}`;
        originalRequest.headers['Authorization'] = `Bearer ${access}`;

        return axiosInstance(originalRequest);
      } catch (refreshError) {
        // Remove both old and new auth-related items
        localStorage.removeItem('token');
        localStorage.removeItem('access_token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        localStorage.removeItem('user_data');
        window.location.href = '/signin';
        return Promise.reject(refreshError);
      }
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;

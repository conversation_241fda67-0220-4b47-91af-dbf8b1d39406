#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_student_status_debug():
    print("🔍 Debugging Student Status Display Issue...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Get a student to test with
    student = Student.objects.first()
    if not student:
        print("❌ No students found")
        return
        
    print(f"\n📋 Debugging student: {student.user.first_name} {student.user.last_name}")
    print(f"   - Database is_active: {student.user.is_active}")
    print(f"   - Database is_active type: {type(student.user.is_active)}")
    
    # Test API response
    headers = {'Authorization': f'Bearer {access_token}'}
    url = f'http://localhost:8000/api/users/students/{student.user.id}/'
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n📡 API Response Structure:")
            print(f"   - Response keys: {list(data.keys())}")
            
            if 'user' in data:
                print(f"   - User object exists: {data['user'] is not None}")
                print(f"   - User keys: {list(data['user'].keys()) if data['user'] else 'None'}")
                if data['user'] and 'is_active' in data['user']:
                    print(f"   - User is_active: {data['user']['is_active']}")
                    print(f"   - User is_active type: {type(data['user']['is_active'])}")
                else:
                    print(f"   - ❌ is_active field missing from user object")
            else:
                print(f"   - ❌ User object missing from response")
                
            # Check if is_active is at the top level
            if 'is_active' in data:
                print(f"   - Top-level is_active: {data['is_active']}")
                print(f"   - Top-level is_active type: {type(data['is_active'])}")
                
            print(f"\n📄 Full API Response:")
            print(json.dumps(data, indent=2, default=str))
            
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")

if __name__ == "__main__":
    test_student_status_debug()

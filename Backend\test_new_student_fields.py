#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_new_student_fields():
    print("🔍 Testing New Student Fields in Database...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    
    # Test 1: Check if new fields exist in model
    print(f"\n🔍 Test 1: Checking if new fields exist in Student model...")
    
    student = Student.objects.first()
    if not student:
        print("❌ No students found for testing")
        return
    
    new_fields = [
        'guardian_name', 'guardian_contact', 'guardian_email', 
        'guardian_relationship', 'guardian_occupation',
        'blood_type', 'medical_conditions',
        'nationality', 'religion'
    ]
    
    for field in new_fields:
        if hasattr(student, field):
            print(f"   ✅ {field}: Field exists in model")
        else:
            print(f"   ❌ {field}: Field missing from model")
    
    # Test 2: Update a student with new field data
    print(f"\n🔍 Test 2: Testing API update with new fields...")
    
    test_data = {
        'guardian_name': 'John Doe',
        'guardian_contact': '+254712345678',
        'guardian_email': '<EMAIL>',
        'guardian_relationship': 'Father',
        'guardian_occupation': 'Engineer',
        'blood_type': 'O+',
        'medical_conditions': 'No known allergies',
        'nationality': 'Kenyan',
        'religion': 'Christian'
    }
    
    student_url = f'http://localhost:8000/api/users/students/{student.user.id}/'
    
    try:
        response = requests.patch(student_url, json=test_data, headers=headers, timeout=10)
        print(f"   📡 API Response: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ API update successful!")
            
            # Verify data was saved
            student.refresh_from_db()
            
            print(f"   🔍 Verifying saved data:")
            for field, expected_value in test_data.items():
                actual_value = getattr(student, field, None)
                if actual_value == expected_value:
                    print(f"      ✅ {field}: '{actual_value}' (correct)")
                else:
                    print(f"      ❌ {field}: Expected '{expected_value}', got '{actual_value}'")
                    
        else:
            print(f"   ❌ API update failed: {response.status_code}")
            print(f"   Error: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ API request failed: {str(e)}")
    
    # Test 3: Retrieve student data via API
    print(f"\n🔍 Test 3: Testing API retrieval of new fields...")
    
    try:
        response = requests.get(student_url, headers=headers, timeout=10)
        print(f"   📡 API Response: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API retrieval successful!")
            
            print(f"   🔍 Checking new fields in API response:")
            for field in new_fields:
                if field in data:
                    value = data[field]
                    print(f"      ✅ {field}: '{value}' (present in API)")
                else:
                    print(f"      ❌ {field}: Missing from API response")
                    
        else:
            print(f"   ❌ API retrieval failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ API request failed: {str(e)}")
    
    # Test 4: Check database schema
    print(f"\n🔍 Test 4: Checking database schema...")
    
    from django.db import connection
    
    with connection.cursor() as cursor:
        cursor.execute("PRAGMA table_info(users_student);")
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        
        print(f"   📋 Database columns found:")
        for field in new_fields:
            if field in column_names:
                print(f"      ✅ {field}: Column exists in database")
            else:
                print(f"      ❌ {field}: Column missing from database")
    
    print(f"\n🎯 New student fields test complete!")
    print(f"\n📊 Summary:")
    print(f"   - Model fields: {'✅' if all(hasattr(student, field) for field in new_fields) else '❌'}")
    print(f"   - API update: {'✅' if response.status_code == 200 else '❌'}")
    print(f"   - Database schema: {'✅' if all(field in column_names for field in new_fields) else '❌'}")
    
    if all(hasattr(student, field) for field in new_fields) and response.status_code == 200:
        print(f"\n🎉 All new student fields are working correctly!")
        print(f"✅ The enhanced student form is ready for testing!")
    else:
        print(f"\n❌ Some issues found. Check the details above.")

if __name__ == "__main__":
    test_new_student_fields()

#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_all_students_visible():
    print("🔍 Testing All Students Visibility...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Database counts
    total_students_db = Student.objects.count()
    students_with_branch = Student.objects.filter(school_branch__isnull=False).count()
    students_without_branch = Student.objects.filter(school_branch__isnull=True).count()
    
    print(f"\n📊 Database Breakdown:")
    print(f"   - Total students: {total_students_db}")
    print(f"   - Students with branch: {students_with_branch}")
    print(f"   - Students without branch: {students_without_branch}")
    
    headers = {'Authorization': f'Bearer {access_token}'}
    base_url = 'http://localhost:8000/api/users/students/'
    
    # Test 1: All students (no branch filter)
    print(f"\n🔍 Test 1: All students (no branch filter)")
    params1 = {'page_size': 100}
    response1 = requests.get(base_url, headers=headers, params=params1, timeout=10)
    
    if response1.status_code == 200:
        data1 = response1.json()
        count1 = data1.get('count', 0)
        results1 = len(data1.get('results', []))
        print(f"   ✅ API returned {count1} students ({results1} in results)")
        
        if count1 == total_students_db:
            print(f"   ✅ Perfect match with database!")
        else:
            print(f"   ⚠️ Mismatch: API {count1} vs DB {total_students_db}")
    else:
        print(f"   ❌ Request failed: {response1.status_code}")
    
    # Test 2: Students with specific branch
    print(f"\n🔍 Test 2: Students with specific branch")
    # Get a branch that has students
    from users.models import SchoolBranch
    branch_with_students = None
    for branch in SchoolBranch.objects.all():
        if Student.objects.filter(school_branch=branch).exists():
            branch_with_students = branch
            break
    
    if branch_with_students:
        params2 = {'page_size': 100, 'school_branch': branch_with_students.id}
        response2 = requests.get(base_url, headers=headers, params=params2, timeout=10)
        
        if response2.status_code == 200:
            data2 = response2.json()
            count2 = data2.get('count', 0)
            expected_count = Student.objects.filter(school_branch=branch_with_students).count()
            print(f"   ✅ Branch '{branch_with_students.name}': API {count2} vs DB {expected_count}")
            
            if count2 == expected_count:
                print(f"   ✅ Branch filtering working correctly!")
            else:
                print(f"   ⚠️ Branch filtering mismatch")
        else:
            print(f"   ❌ Branch request failed: {response2.status_code}")
    else:
        print(f"   ⚠️ No branches with students found")
    
    # Test 3: Show student details
    print(f"\n📋 Student Details:")
    all_students = Student.objects.all()
    for i, student in enumerate(all_students[:5]):  # Show first 5
        branch_name = student.school_branch.name if student.school_branch else "No Branch"
        print(f"   {i+1}. {student.user.first_name} {student.user.last_name} - {branch_name} - Active: {student.user.is_active}")
    
    if all_students.count() > 5:
        print(f"   ... and {all_students.count() - 5} more students")
    
    print(f"\n🎯 Visibility test complete!")

if __name__ == "__main__":
    test_all_students_visible()

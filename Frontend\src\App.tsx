import React, { Suspense } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import FirstTimeLoginWrapper from "./components/auth/FirstTimeLoginWrapper.tsx";
import SignIn from "./pages/AuthPages/SignIn.tsx";
import SignUp from "./pages/AuthPages/SignUp.tsx";
import ForgotPassword from "./pages/AuthPages/ForgotPassword.tsx";
import ResetPassword from "./pages/AuthPages/ResetPassword.tsx";
import ChangePassword from "./pages/AuthPages/ChangePassword.tsx";
import RegisterForm from "./components/auth/RegisterForm.tsx";
import RegisterStepOne from "./components/auth/RegisterStepOne.tsx";
import CompleteProfile from "./pages/AuthPages/CompleteProfile.tsx";
import AcceptInvitation from "./pages/AuthPages/AcceptInvitation.tsx";
import RegisterQR from "./pages/AuthPages/RegisterQR.tsx";
import NotFound from "./pages/OtherPage/NotFound";
import NotAuthorized from "./pages/OtherPage/NotAuthorized";
import UserProfiles from "./pages/UserProfiles";
import Videos from "./pages/UiElements/Videos";
import Images from "./pages/UiElements/Images";
import Alerts from "./pages/UiElements/Alerts";
import Badges from "./pages/UiElements/Badges";
import Avatars from "./pages/UiElements/Avatars";
import Buttons from "./pages/UiElements/Buttons";
import LineChart from "./pages/Charts/LineChart";
import BarChart from "./pages/Charts/BarChart";
import Calendar from "./pages/Announcements/Calendar";
import BasicTables from "./pages/Tables/BasicTables";
import FormElements from "./pages/Forms/FormElements";
import Blank from "./pages/Blank";
import DebugApiTest from "./pages/DebugApiTest";
import DashboardTest from "./pages/DashboardTest";
import AppLayout from "./layout/AppLayout";
import { ScrollToTop } from "./components/common/ScrollToTop";
import Home from "./pages/Dashboard/Home";
import { ProtectedRoute } from './components/ProtectedRoute';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './store/store';
import { AuthProvider } from './context/AuthContext';
import { NotificationProvider } from './context/NotificationContext';
import { SchoolProvider } from './contexts/SchoolContext';
import { ModuleProvider } from './contexts/ModuleContext';
import { SectionPermissionProvider } from './contexts/SectionPermissionContext';
import { ThemeProvider } from './context/ThemeContext';
import AllStudents from "./pages/Students/AllStudents";
import ViewStudent from "./pages/Students/ViewStudent";
import EditStudent from "./pages/Students/EditStudent";
import InactiveStudents from "./pages/Students/InactiveStudents";
import UnassignedStudents from "./pages/students/UnassignedStudents";
import Assignments from "./pages/Students/Assignments";
import Grades from "./pages/Students/Grades";
import Announcements from "./pages/Announcements/Announcements";
import PerformanceAnalytics from "./pages/Students/PerformanceAnalytics";
import ClassTimeTable from "./pages/Students/ClassTimeTable";
import ExamResults from "./pages/Students/ExamResults";
import Assessments from "./pages/Students/Assessments";

// Documentation Pages
const UserGuide = React.lazy(() => import('./pages/documentation/user-guide/index'));
const AcademicsUserGuide = React.lazy(() => import('./pages/documentation/user-guide/academics'));
const LicenseManagementGuide = React.lazy(() => import('./pages/documentation/user-guide/license-management'));
const TrainingResources = React.lazy(() => import('./pages/documentation/training-resources/index'));
const AcademicsTraining = React.lazy(() => import('./pages/documentation/training-resources/academics'));
const FAQsPage = React.lazy(() => import('./pages/documentation/faqs/index'));
const LicenseManagementFAQs = React.lazy(() => import('./pages/documentation/faqs/license-management'));

// Admin Pages
import AdminDashboard from "./pages/admin/Dashboard";
import UserManagement from "./pages/admin/UserManagement";
import SchoolsManagement from "./pages/admin/SchoolsManagement";
import SchoolBranchesManagement from "./pages/admin/SchoolBranchesManagement";
import SchoolForm from "./pages/admin/SchoolForm";
import BranchForm from "./pages/admin/BranchForm";
import SchoolManagement from "./pages/admin/SchoolManagement";
import CreateSchoolPage from "./pages/admin/CreateSchoolPage";
// Admin curriculum config now uses the unified component

// Library Pages
import LibraryDashboard from "./pages/library/LibraryDashboard";
import BooksPage from "./pages/library/BooksPage";
import EResourcesPage from "./pages/library/EResourcesPage";
import BorrowingsPage from "./pages/library/BorrowingsPage";

// Inventory Pages
import InventoryDashboard from "./pages/inventory/InventoryDashboard";
import AssetsPage from "./pages/inventory/AssetsPage";
import SuppliesPage from "./pages/inventory/SuppliesPage";
import MaintenancePage from "./pages/inventory/MaintenancePage";

// Communication Pages
import CommunicationDashboard from "./pages/communication/CommunicationDashboard";
import AnnouncementsPage from "./pages/communication/AnnouncementsPage";
import MessagesPage from "./pages/communication/MessagesPage";
import EventsPage from "./pages/communication/EventsPage";

// Settings Pages
import SettingsDashboard from "./pages/settings/SettingsDashboard";
import NewSettingsDashboard from "./pages/settings/NewSettingsDashboard";
import WizardsDashboard from "./pages/settings/WizardsDashboard";
import SchoolProfilePage from "./pages/settings/SchoolProfilePage";
import SystemConfigPage from "./pages/settings/SystemConfigPage";
import RolesPage from "./pages/settings/RolesPage";
import SettingsPage from "./pages/SettingsPage";

// Curriculum Pages
import CurriculumDashboardPage from "./pages/academics/CurriculumDashboardPage";
import CurriculumConfigPage from "./pages/academics/CurriculumConfigPage";
import CurriculumSystemsPage from "./pages/academics/CurriculumSystemsPage";
import CurriculumSystemFormPage from "./pages/academics/CurriculumSystemFormPage";
import EducationLevelsPage from "./pages/academics/EducationLevelsPage";
import ClassesPage from "./pages/academics/ClassesPage";
import ClassProgressionPage from "./pages/academics/ClassProgressionPage";
import ClassProgressionFormPage from "./pages/academics/ClassProgressionFormPage";
import StreamsPage from "./pages/academics/StreamsPage";
import SubjectsPage from "./pages/academics/SubjectsPage";
import SubjectFormPage from "./pages/academics/SubjectFormPage";
import SubjectEnrollmentPage from "./pages/academics/SubjectEnrollmentPage";
import DepartmentsPage from "./pages/academics/DepartmentsPage";
import DepartmentFormPage from "./pages/academics/DepartmentFormPage";
import GradingSystemPage from "./pages/academics/GradingSystemPage";
import GradingSystemFormPage from "./pages/academics/GradingSystemFormPage";
import SyllabusPage from "./pages/academics/SyllabusPage";
import SyllabusFormPage from "./pages/academics/SyllabusFormPage";
import SyllabusDetailPage from "./pages/academics/SyllabusDetailPage";
import SyllabusTemplateListPage from "./pages/academics/SyllabusTemplateListPage";
import SyllabusTemplateFormPage from "./pages/academics/SyllabusTemplateFormPage";
import SyllabusTemplateDetailPage from "./pages/academics/SyllabusTemplateDetailPage";
import ApplySyllabusTemplatePage from "./pages/academics/ApplySyllabusTemplatePage";
import AcademicManagementDashboardPage from "./pages/academics/AcademicManagementDashboardPage";
import AcademicYearsPage from "./pages/academics/AcademicYearsPage";
import AcademicYearDetailsPage from "./pages/academics/AcademicYearDetailsPage";
import AcademicYearEditPage from "./pages/academics/AcademicYearEditPage";
import AcademicYearTemplateListPage from "./pages/academics/AcademicYearTemplateListPage";
import AcademicYearTemplateEditPage from "./pages/academics/AcademicYearTemplateEditPage";
import ApplyTemplatePage from "./pages/academics/ApplyTemplatePage";
import BatchEditAcademicYearsPage from "./pages/academics/BatchEditAcademicYearsPage";
import StudentRegistrationPage from "./pages/Students/StudentRegistrationPage";
import GroupManagement from "./pages/admin/GroupManagement";
import UserCreationPage from "./pages/UserCreationPage";
import UserEdit from "./components/users/UserEdit";
import Permissions from "./pages/settings/Permissions";
import SchoolSelectionPage from "./pages/schools/SchoolSelectionPage";
import LicenseSettingsPage from "./pages/settings/LicenseSettingsPage";
import LicenseManagementPage from "./pages/settings/LicenseManagementPage";
import CreateLicensePage from "./pages/settings/CreateLicensePage";
import EditLicensePage from "./pages/settings/EditLicensePage";
import RenewLicensePage from "./pages/settings/RenewLicensePage";
import LicenseUsageDashboardPage from "./pages/settings/LicenseUsageDashboardPage";

// Billing/Finance Pages
import ParentFeeDashboard from "./pages/ParentFeeDashboard";
import FinanceAnalytics from "./pages/admin/FinanceAnalytics";
import FeeManagement from "./pages/admin/FeeManagement";
import PaymentPage from "./pages/PaymentPage";
import SchoolBillingDashboard from "./pages/dashboards/SchoolBillingDashboard";
import UserGuides from "./pages/UserGuides";

export default function App() {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <AuthProvider>
          <NotificationProvider>
            <SchoolProvider>
              <ModuleProvider>
                <ThemeProvider>
                  <SectionPermissionProvider>
                  <Router>
                <ToastContainer position="top-right" autoClose={5000} hideProgressBar={false} newestOnTop closeOnClick rtl={false} pauseOnFocusLoss draggable pauseOnHover />
                <ScrollToTop />
                <FirstTimeLoginWrapper>
                <Routes>
            <Route element={<ProtectedRoute><AppLayout /></ProtectedRoute>}>
              {/* Main dashboard route - will redirect to role-specific dashboard */}
              <Route path="/" element={<ProtectedRoute allowedRoles={['admin', 'system_admin', 'teacher', 'student', 'parent']}><Home /></ProtectedRoute>} />

              {/* Role-specific dashboard routes */}
              <Route path="/admin" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><AdminDashboard /></ProtectedRoute>} />
              <Route path="/admin/dashboard" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><AdminDashboard /></ProtectedRoute>} />
              <Route path="/teacher/dashboard" element={<ProtectedRoute allowedRoles={['teacher']}><Home /></ProtectedRoute>} />
              <Route path="/student/dashboard" element={<ProtectedRoute allowedRoles={['student']}><Home /></ProtectedRoute>} />
              <Route path="/parent/dashboard" element={<ProtectedRoute allowedRoles={['parent']}><ParentFeeDashboard /></ProtectedRoute>} />

              {/* Billing/Finance Routes */}
              <Route path="/fees" element={<ProtectedRoute allowedRoles={['parent']}><ParentFeeDashboard /></ProtectedRoute>} />
              <Route path="/fees/payment" element={<ProtectedRoute allowedRoles={['parent', 'admin', 'finance']}><PaymentPage /></ProtectedRoute>} />
              <Route path="/school-billing" element={<ProtectedRoute allowedRoles={['admin', 'school_admin', 'principal']}><SchoolBillingDashboard /></ProtectedRoute>} />
              <Route path="/user-guides" element={<ProtectedRoute allowedRoles={['admin', 'school_admin', 'principal', 'teacher', 'parent']}><UserGuides /></ProtectedRoute>} />
              <Route path="/admin/fees" element={<ProtectedRoute allowedRoles={['admin', 'finance', 'system_admin']}><FeeManagement /></ProtectedRoute>} />
              <Route path="/admin/finance/analytics" element={<ProtectedRoute allowedRoles={['admin', 'finance', 'system_admin']}><FinanceAnalytics /></ProtectedRoute>} />
              <Route path="/finance/analytics" element={<ProtectedRoute allowedRoles={['admin', 'finance', 'system_admin']}><FinanceAnalytics /></ProtectedRoute>} />
              <Route path="/admin/schools-management" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><SchoolManagement /></ProtectedRoute>} />
              <Route path="/admin/users" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><UserManagement /></ProtectedRoute>} />
              <Route path="/admin/users/create" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><UserCreationPage /></ProtectedRoute>} />
              <Route path="/admin/users/edit/:id" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><UserEdit /></ProtectedRoute>} />
              <Route path="/admin/groups" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><GroupManagement /></ProtectedRoute>} />
              <Route path="/admin/schools" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><SchoolsManagement /></ProtectedRoute>} />
              <Route path="/admin/schools/add" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><SchoolForm /></ProtectedRoute>} />
              <Route path="/admin/schools/create" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><CreateSchoolPage /></ProtectedRoute>} />
              <Route path="/admin/schools/edit/:schoolId" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><SchoolForm /></ProtectedRoute>} />
              <Route path="/admin/schools/:schoolId/branches" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><SchoolBranchesManagement /></ProtectedRoute>} />
              <Route path="/admin/schools/:schoolId/branches/add" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><BranchForm /></ProtectedRoute>} />
              <Route path="/admin/schools/:schoolId/branches/edit/:branchId" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><BranchForm /></ProtectedRoute>} />

              {/* Admin License Management Routes */}
              <Route path="/admin/licenses" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><LicenseManagementPage /></ProtectedRoute>} />
              <Route path="/admin/licenses/create" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><CreateLicensePage /></ProtectedRoute>} />
              <Route path="/admin/licenses/edit/:id" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><EditLicensePage /></ProtectedRoute>} />
              <Route path="/admin/licenses/renew/:id" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><RenewLicensePage /></ProtectedRoute>} />
              {/* Admin curriculum config now uses the unified component */}
              <Route path="/calendar" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><Calendar /></ProtectedRoute>} />
              <Route path="/AllStudents" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><AllStudents /></ProtectedRoute>} />
              <Route path="/inactive-students" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><InactiveStudents /></ProtectedRoute>} />
              <Route path="/unassigned-students" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><UnassignedStudents /></ProtectedRoute>} />
              <Route path="/student/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><ViewStudent /></ProtectedRoute>} />
              <Route path="/edit-student/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><EditStudent /></ProtectedRoute>} />
              <Route path="/students/register" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><StudentRegistrationPage /></ProtectedRoute>} />
              <Route path="/assignments" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><Assignments /></ProtectedRoute>} />
              <Route path="/grades" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><Grades /></ProtectedRoute>} />
              <Route path="/announcements" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><Announcements /></ProtectedRoute>} />
              <Route path="/performance-analytics" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><PerformanceAnalytics /></ProtectedRoute>} />
              <Route path="/class-timetable" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><ClassTimeTable /></ProtectedRoute>} />
              <Route path="/exam-results" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><ExamResults /></ProtectedRoute>} />
              <Route path="/assessments" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><Assessments /></ProtectedRoute>} />
              <Route path="/profile" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'student', 'parent']}><UserProfiles /></ProtectedRoute>} />

              {/* Role-specific profile routes */}
              <Route path="/admin/profile" element={<ProtectedRoute allowedRoles={['admin', 'system_admin', 'school_admin', 'deputy_principal', 'branch_admin', 'department_head', 'ict_admin']}><UserProfiles /></ProtectedRoute>} />
              <Route path="/teacher/profile" element={<ProtectedRoute allowedRoles={['teacher']}><UserProfiles /></ProtectedRoute>} />
              <Route path="/student/profile" element={<ProtectedRoute allowedRoles={['student']}><UserProfiles /></ProtectedRoute>} />
              <Route path="/parent/profile" element={<ProtectedRoute allowedRoles={['parent']}><UserProfiles /></ProtectedRoute>} />
              <Route path="/staff/profile" element={<ProtectedRoute allowedRoles={['staff', 'librarian', 'counselor', 'accountant', 'secretary', 'nurse', 'maintenance', 'security', 'driver']}><UserProfiles /></ProtectedRoute>} />

              {/* Library Routes */}
              <Route path="/library" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'librarian']}><LibraryDashboard /></ProtectedRoute>} />
              <Route path="/library/books" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'librarian']}><BooksPage /></ProtectedRoute>} />
              <Route path="/library/e-resources" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'librarian']}><EResourcesPage /></ProtectedRoute>} />
              <Route path="/library/borrowings" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'librarian']}><BorrowingsPage /></ProtectedRoute>} />

              {/* Inventory Routes */}
              <Route path="/inventory" element={<ProtectedRoute allowedRoles={['admin', 'inventory_manager']}><InventoryDashboard /></ProtectedRoute>} />
              <Route path="/inventory/assets" element={<ProtectedRoute allowedRoles={['admin', 'inventory_manager']}><AssetsPage /></ProtectedRoute>} />
              <Route path="/inventory/supplies" element={<ProtectedRoute allowedRoles={['admin', 'inventory_manager']}><SuppliesPage /></ProtectedRoute>} />
              <Route path="/inventory/maintenance" element={<ProtectedRoute allowedRoles={['admin', 'inventory_manager']}><MaintenancePage /></ProtectedRoute>} />

              {/* Settings Routes */}
              <Route path="/settings/permissions" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><Permissions /></ProtectedRoute>} />

              {/* Communication Routes */}
              <Route path="/communication" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'student', 'parent']}><CommunicationDashboard /></ProtectedRoute>} />
              <Route path="/communication/announcements" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><AnnouncementsPage /></ProtectedRoute>} />
              <Route path="/communication/messages" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'student', 'parent']}><MessagesPage /></ProtectedRoute>} />
              <Route path="/communication/events" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><EventsPage /></ProtectedRoute>} />

              {/* Settings Routes */}
              <Route path="/settings" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><NewSettingsDashboard /></ProtectedRoute>} />
              <Route path="/settings/old" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><SettingsDashboard /></ProtectedRoute>} />
              <Route path="/settings/wizards" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><WizardsDashboard /></ProtectedRoute>} />
              <Route path="/settings/school-profile" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><SchoolProfilePage /></ProtectedRoute>} />
              <Route path="/settings/system-config" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><SystemConfigPage /></ProtectedRoute>} />
              <Route path="/settings/roles" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><RolesPage /></ProtectedRoute>} />
              <Route path="/settings/modules" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><SettingsPage /></ProtectedRoute>} />
              {/* License Management Routes - Clear and Organized */}
              <Route path="/settings/license/overview" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><LicenseSettingsPage /></ProtectedRoute>} />
              <Route path="/settings/license/analytics" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><LicenseUsageDashboardPage /></ProtectedRoute>} />
              <Route path="/settings/license/manage" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><LicenseManagementPage /></ProtectedRoute>} />
              <Route path="/settings/license/create" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><CreateLicensePage /></ProtectedRoute>} />
              <Route path="/settings/license/edit/:id" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><EditLicensePage /></ProtectedRoute>} />
              <Route path="/settings/license/renew/:id" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><RenewLicensePage /></ProtectedRoute>} />

              {/* Legacy redirects for backward compatibility */}
              <Route path="/settings/license" element={<Navigate to="/settings/license/overview" replace />} />
              <Route path="/settings/licenses/usage" element={<Navigate to="/settings/license/analytics" replace />} />
              <Route path="/settings/licenses/manage" element={<Navigate to="/settings/license/manage" replace />} />
              <Route path="/settings/licenses/create" element={<Navigate to="/settings/license/create" replace />} />
              <Route path="/settings/licenses/edit/:id" element={<Navigate to="/settings/license/edit/:id" replace />} />
              <Route path="/settings/licenses/renew/:id" element={<Navigate to="/settings/license/renew/:id" replace />} />

              {/* Academic Management Routes */}
              <Route path="/academics" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'staff']}><AcademicManagementDashboardPage /></ProtectedRoute>} />
              <Route path="/academics/academic-years" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><AcademicYearsPage /></ProtectedRoute>} />
              <Route path="/academics/academic-years/new" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><AcademicYearEditPage /></ProtectedRoute>} />
              <Route path="/academics/academic-years/batch-edit" element={<ProtectedRoute allowedRoles={['admin']}><BatchEditAcademicYearsPage /></ProtectedRoute>} />
              <Route path="/academics/academic-years/edit/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><AcademicYearEditPage /></ProtectedRoute>} />
              <Route path="/academics/academic-years/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><AcademicYearDetailsPage /></ProtectedRoute>} />

              {/* Academic Year Templates Routes */}
              <Route path="/academics/templates/academic-years" element={<ProtectedRoute allowedRoles={['admin']}><AcademicYearTemplateListPage /></ProtectedRoute>} />
              <Route path="/academics/templates/academic-years/new" element={<ProtectedRoute allowedRoles={['admin']}><AcademicYearTemplateEditPage /></ProtectedRoute>} />
              <Route path="/academics/templates/academic-years/:id" element={<ProtectedRoute allowedRoles={['admin']}><AcademicYearTemplateEditPage /></ProtectedRoute>} />
              <Route path="/academics/templates/academic-years/:id/apply" element={<ProtectedRoute allowedRoles={['admin']}><ApplyTemplatePage /></ProtectedRoute>} />

              {/* Curriculum Routes */}
              <Route path="/academics/curriculum" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><CurriculumDashboardPage /></ProtectedRoute>} />
              <Route path="/academics/curriculum/config" element={<ProtectedRoute allowedRoles={['admin']}><CurriculumConfigPage /></ProtectedRoute>} />
              <Route path="/academics/curriculum/systems" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><CurriculumSystemsPage /></ProtectedRoute>} />
              <Route path="/academics/curriculum/systems/new" element={<ProtectedRoute allowedRoles={['admin']}><CurriculumSystemFormPage /></ProtectedRoute>} />
              <Route path="/academics/curriculum/systems/edit/:id" element={<ProtectedRoute allowedRoles={['admin']}><CurriculumSystemFormPage /></ProtectedRoute>} />
              <Route path="/academics/curriculum/levels" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><EducationLevelsPage /></ProtectedRoute>} />

              {/* Classes Routes */}
              <Route path="/academics/classes" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><ClassesPage /></ProtectedRoute>} />
              <Route path="/academics/classes/progression" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><ClassProgressionPage /></ProtectedRoute>} />
              <Route path="/academics/classes/progression/new" element={<ProtectedRoute allowedRoles={['admin']}><ClassProgressionFormPage /></ProtectedRoute>} />
              <Route path="/academics/classes/progression/edit/:id" element={<ProtectedRoute allowedRoles={['admin']}><ClassProgressionFormPage /></ProtectedRoute>} />

              {/* Streams & Subjects Routes */}
              <Route path="/academics/streams" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><StreamsPage /></ProtectedRoute>} />
              <Route path="/academics/subjects" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SubjectsPage /></ProtectedRoute>} />
              <Route path="/academics/subjects/new" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SubjectFormPage /></ProtectedRoute>} />
              <Route path="/academics/subjects/:id/edit" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SubjectFormPage /></ProtectedRoute>} />
              <Route path="/academics/subject-enrollment" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SubjectEnrollmentPage /></ProtectedRoute>} />

              {/* Departments Routes */}
              <Route path="/academics/departments" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><DepartmentsPage /></ProtectedRoute>} />
              <Route path="/academics/departments/new" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><DepartmentFormPage /></ProtectedRoute>} />
              <Route path="/academics/departments/edit/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><DepartmentFormPage /></ProtectedRoute>} />

              {/* Grading System Routes */}
              <Route path="/academics/grading-system" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><GradingSystemPage /></ProtectedRoute>} />
              <Route path="/academics/grading-system/new" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><GradingSystemFormPage /></ProtectedRoute>} />
              <Route path="/academics/grading-system/edit/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><GradingSystemFormPage /></ProtectedRoute>} />

              {/* Syllabus Routes */}
              <Route path="/academics/syllabus" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SyllabusPage /></ProtectedRoute>} />
              <Route path="/academics/syllabus/new" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SyllabusFormPage /></ProtectedRoute>} />
              <Route path="/academics/syllabus/edit/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SyllabusFormPage /></ProtectedRoute>} />
              <Route path="/academics/syllabus/duplicate/:duplicateId" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SyllabusFormPage /></ProtectedRoute>} />
              <Route path="/academics/syllabus/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SyllabusDetailPage /></ProtectedRoute>} />

              {/* Syllabus Templates Routes */}
              <Route path="/academics/syllabus/templates" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SyllabusTemplateListPage /></ProtectedRoute>} />
              <Route path="/academics/syllabus/templates/new" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SyllabusTemplateFormPage /></ProtectedRoute>} />
              <Route path="/academics/syllabus/templates/edit/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SyllabusTemplateFormPage /></ProtectedRoute>} />
              <Route path="/academics/syllabus/templates/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><SyllabusTemplateDetailPage /></ProtectedRoute>} />
              <Route path="/academics/syllabus/templates/apply/:id" element={<ProtectedRoute allowedRoles={['admin', 'teacher']}><ApplySyllabusTemplatePage /></ProtectedRoute>} />

              {/* Documentation Routes */}
              <Route path="/user-guide" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'student', 'parent']}>
                <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
                  <UserGuide />
                </Suspense>
              </ProtectedRoute>} />
              <Route path="/user-guide/academics" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'student', 'parent']}>
                <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
                  <AcademicsUserGuide />
                </Suspense>
              </ProtectedRoute>} />
              <Route path="/user-guide/license-management" element={<ProtectedRoute allowedRoles={['admin']}>
                <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
                  <LicenseManagementGuide />
                </Suspense>
              </ProtectedRoute>} />
              <Route path="/faqs" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'student', 'parent']}>
                <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
                  <FAQsPage />
                </Suspense>
              </ProtectedRoute>} />
              <Route path="/faqs/license-management" element={<ProtectedRoute allowedRoles={['admin']}>
                <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
                  <LicenseManagementFAQs />
                </Suspense>
              </ProtectedRoute>} />
              <Route path="/training-resources" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'student', 'parent']}>
                <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
                  <TrainingResources />
                </Suspense>
              </ProtectedRoute>} />
              <Route path="/training-resources/academics" element={<ProtectedRoute allowedRoles={['admin', 'teacher', 'student', 'parent']}>
                <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
                  <AcademicsTraining />
                </Suspense>
              </ProtectedRoute>} />

              <Route path="/blank" element={<Blank />} />
              <Route path="/not-authorized" element={<NotAuthorized />} />
              <Route path="/form-elements" element={<FormElements />} />
              <Route path="/basic-tables" element={<BasicTables />} />
              <Route path="/alerts" element={<Alerts />} />
              <Route path="/avatars" element={<Avatars />} />
              <Route path="/badge" element={<Badges />} />
              <Route path="/buttons" element={<Buttons />} />
              <Route path="/images" element={<Images />} />
              <Route path="/videos" element={<Videos />} />
              <Route path="/line-chart" element={<LineChart />} />
              <Route path="/bar-chart" element={<BarChart />} />
            </Route>

            <Route path="/signin" element={<SignIn />} />
            <Route path="/signup" element={<SignUp />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/password-reset/:token" element={<ResetPassword />} />
            <Route path="/change-password" element={<ProtectedRoute><ChangePassword /></ProtectedRoute>} />
            <Route path="/register" element={<RegisterForm />} />
            <Route path="/register-step-one" element={<RegisterStepOne />} />
            <Route path="/register-step-two/:userId" element={<CompleteProfile />} />
            <Route path="/complete-profile" element={<ProtectedRoute><CompleteProfile /></ProtectedRoute>} />
            <Route path="/accept-invitation/:token" element={<AcceptInvitation />} />
            <Route path="/register-qr/:token" element={<RegisterQR />} />
            <Route path="/schools/select" element={<ProtectedRoute><SchoolSelectionPage /></ProtectedRoute>} />
            <Route path="/debug-api" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><DebugApiTest /></ProtectedRoute>} />
            <Route path="/dashboard-test" element={<ProtectedRoute allowedRoles={['admin', 'system_admin']}><DashboardTest /></ProtectedRoute>} />
            <Route path="*" element={<NotFound />} />
          </Routes>
              </FirstTimeLoginWrapper>
            </Router>
                </SectionPermissionProvider>
                </ThemeProvider>
              </ModuleProvider>
            </SchoolProvider>
          </NotificationProvider>
        </AuthProvider>
      </PersistGate>
    </Provider>
  );
}

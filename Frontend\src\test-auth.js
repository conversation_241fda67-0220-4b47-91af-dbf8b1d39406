// Simple test to check authentication status
console.log('🔍 Testing Authentication...');

const token = localStorage.getItem('access_token');
console.log('Access token exists:', !!token);
console.log('Token length:', token ? token.length : 0);

if (token) {
  // Test with axios instance
  import('./components/utils/AxiosInstanceFixed.tsx').then(({ default: axiosInstance }) => {
    console.log('Testing with axios instance...');
    
    axiosInstance.get('/schools/schools/')
      .then(response => {
        console.log('✅ Schools API success:', response.data.results?.length || 0, 'schools');
      })
      .catch(error => {
        console.error('❌ Schools API failed:', error.response?.status, error.response?.data);
      });
      
    axiosInstance.get('/schools/branch/')
      .then(response => {
        console.log('✅ Branches API success:', response.data.results?.length || 0, 'branches');
      })
      .catch(error => {
        console.error('❌ Branches API failed:', error.response?.status, error.response?.data);
      });
  });
} else {
  console.log('❌ No access token found');
}

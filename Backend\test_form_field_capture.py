#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_form_field_capture():
    print("🔍 Testing Form Field Capture and Save...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    
    # Get a student for testing
    student = Student.objects.first()
    if not student:
        print("❌ No students found for testing")
        return
    
    print(f"\n📋 Testing with student: {student.user.first_name} {student.user.last_name}")
    print(f"   📋 Student User ID: {student.user.id}")
    
    # Test comprehensive form data (simulating what the frontend form would send)
    print(f"\n🔍 Test 1: Comprehensive form data update...")
    
    comprehensive_data = {
        # User information
        'first_name': 'Updated John',
        'last_name': 'Updated Doe',
        'email': '<EMAIL>',
        'phone': '+254700000000',
        'is_active': True,
        
        # Personal information
        'gender': 'M',
        'date_of_birth': '2005-01-15',
        'address': '123 Updated Street, Nairobi',
        'phone_number': '+254711111111',
        'emergency_contact': '+254722222222',
        'additional_notes': 'Updated additional notes',
        
        # Guardian information (NEW FIELDS)
        'guardian_name': 'Updated Guardian Name',
        'guardian_contact': '+254733333333',
        'guardian_email': '<EMAIL>',
        'guardian_relationship': 'Father',
        'guardian_occupation': 'Updated Engineer',
        
        # Medical information (NEW FIELDS)
        'blood_type': 'A+',
        'medical_conditions': 'Updated medical conditions',
        
        # Additional personal details (NEW FIELDS)
        'nationality': 'Updated Kenyan',
        'religion': 'Updated Christian',
        
        # Academic information
        'admission_number': 'UPD001',
        'admission_date': '2024-01-01',
        'subject_type': 'Compulsory',
        # Skip class_name and stream for now - they need valid IDs
        
        # School information
        'school_branch_id': student.school_branch.id if student.school_branch else None,
    }
    
    student_url = f'http://localhost:8000/api/users/students/{student.user.id}/'
    
    try:
        print(f"   📡 Sending PATCH request to: {student_url}")
        print(f"   📋 Data fields being sent: {list(comprehensive_data.keys())}")
        print(f"   📋 New fields in data: guardian_name, guardian_contact, guardian_email, guardian_relationship, guardian_occupation, blood_type, medical_conditions, nationality, religion")
        
        response = requests.patch(student_url, json=comprehensive_data, headers=headers, timeout=15)
        print(f"   📡 API Response: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ API update successful!")
            
            # Get the response data
            response_data = response.json()
            print(f"   📋 Response contains {len(response_data)} fields")
            
            # Check if new fields are in the response
            new_fields = ['guardian_name', 'guardian_contact', 'guardian_email', 'guardian_relationship', 
                         'guardian_occupation', 'blood_type', 'medical_conditions', 'nationality', 'religion']
            
            print(f"   🔍 Checking new fields in API response:")
            for field in new_fields:
                if field in response_data:
                    value = response_data[field]
                    expected = comprehensive_data[field]
                    if value == expected:
                        print(f"      ✅ {field}: '{value}' (correct)")
                    else:
                        print(f"      ⚠️ {field}: Expected '{expected}', got '{value}'")
                else:
                    print(f"      ❌ {field}: Missing from API response")
            
            # Verify data was saved in database
            student.refresh_from_db()
            print(f"   🔍 Verifying database save:")
            for field in new_fields:
                db_value = getattr(student, field, None)
                expected = comprehensive_data[field]
                if db_value == expected:
                    print(f"      ✅ {field}: '{db_value}' (saved correctly)")
                else:
                    print(f"      ❌ {field}: Expected '{expected}', DB has '{db_value}'")
                    
        elif response.status_code == 400:
            print(f"   ❌ Bad Request (400): {response.text[:300]}")
            try:
                error_data = response.json()
                print(f"   📋 Error details: {error_data}")
            except:
                pass
        else:
            print(f"   ❌ API update failed: {response.status_code}")
            print(f"   Error: {response.text[:300]}")
            
    except Exception as e:
        print(f"   ❌ API request failed: {str(e)}")
        return
    
    # Test 2: Retrieve and verify all fields are returned
    print(f"\n🔍 Test 2: Verifying field retrieval...")
    
    try:
        get_response = requests.get(student_url, headers=headers, timeout=10)
        print(f"   📡 GET Response: {get_response.status_code}")
        
        if get_response.status_code == 200:
            get_data = get_response.json()
            print(f"   ✅ Data retrieval successful!")
            print(f"   📋 Retrieved {len(get_data)} fields")
            
            # Check all expected fields are present
            all_expected_fields = list(comprehensive_data.keys())
            missing_fields = []
            present_fields = []
            
            for field in all_expected_fields:
                if field in get_data:
                    present_fields.append(field)
                else:
                    missing_fields.append(field)
            
            print(f"   📊 Field presence summary:")
            print(f"      ✅ Present: {len(present_fields)}/{len(all_expected_fields)} fields")
            print(f"      ❌ Missing: {len(missing_fields)} fields")
            
            if missing_fields:
                print(f"      Missing fields: {missing_fields}")
                
        else:
            print(f"   ❌ Data retrieval failed: {get_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ GET request failed: {str(e)}")
    
    print(f"\n🎯 Form field capture test complete!")
    print(f"\n📊 Summary:")
    print(f"   - API Update: {'✅' if response.status_code == 200 else '❌'}")
    print(f"   - New Fields Saved: {'✅' if response.status_code == 200 else '❌'}")
    print(f"   - Data Retrieval: {'✅' if get_response.status_code == 200 else '❌'}")
    
    if response.status_code == 200 and get_response.status_code == 200:
        print(f"\n🎉 Form field capture is working correctly!")
        print(f"✅ All new fields are being saved and retrieved properly!")
    else:
        print(f"\n❌ Issues found. Check the details above.")

if __name__ == "__main__":
    test_form_field_capture()

import axiosInstance from '../components/utils/AxiosInstance';

/**
 * Test script to verify dashboard API endpoints
 */
export const testDashboardAPIs = async () => {
  console.log('Testing Dashboard APIs...');
  
  try {
    // Test 1: User counts
    console.log('1. Testing user counts API...');
    try {
      const userCountsResponse = await axiosInstance.get('/core/users/counts/');
      console.log('✅ User counts API working:', userCountsResponse.data);
    } catch (error) {
      console.error('❌ User counts API failed:', error);
    }

    // Test 2: Core dashboard metrics
    console.log('2. Testing core dashboard metrics API...');
    try {
      const coreMetricsResponse = await axiosInstance.get('/core/dashboard/metrics/');
      console.log('✅ Core metrics API working:', coreMetricsResponse.data);
    } catch (error) {
      console.error('❌ Core metrics API failed:', error);
    }

    // Test 3: Academic metrics
    console.log('3. Testing academic metrics API...');
    try {
      const academicMetricsResponse = await axiosInstance.get('/academics/metrics/');
      console.log('✅ Academic metrics API working:', academicMetricsResponse.data);
    } catch (error) {
      console.error('❌ Academic metrics API failed:', error);
    }

    // Test 4: Announcements
    console.log('4. Testing announcements API...');
    try {
      const announcementsResponse = await axiosInstance.get('/communication/announcements/current/');
      console.log('✅ Announcements API working:', announcementsResponse.data);
    } catch (error) {
      console.error('❌ Announcements API failed:', error);
    }

    // Test 5: Events
    console.log('5. Testing events API...');
    try {
      const eventsResponse = await axiosInstance.get('/communication/events/upcoming/');
      console.log('✅ Events API working:', eventsResponse.data);
    } catch (error) {
      console.error('❌ Events API failed:', error);
    }

    // Test 6: School statistics
    console.log('6. Testing school statistics API...');
    try {
      const statsResponse = await axiosInstance.get('/statistics/statistics/school/');
      console.log('✅ School statistics API working:', statsResponse.data);
    } catch (error) {
      console.error('❌ School statistics API failed:', error);
    }

  } catch (error) {
    console.error('❌ General API test failed:', error);
  }
};

// Export for use in components
export default testDashboardAPIs;

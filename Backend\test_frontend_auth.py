#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from core.models import CustomUser

def test_frontend_auth():
    print("🔍 Testing Frontend Authentication Compatibility...")
    
    # Get a superuser for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    refresh_token = str(refresh)
    
    print(f"✅ Generated tokens for user: {admin_user.username}")
    print(f"   📋 Access token: {access_token[:50]}...")
    print(f"   📋 Refresh token: {refresh_token[:50]}...")
    
    # Test 1: Schools API with access token
    print(f"\n🔍 Test 1: Schools API with Bearer token...")
    headers = {'Authorization': f'Bearer {access_token}'}
    
    try:
        schools_response = requests.get('http://localhost:8000/api/schools/schools/', headers=headers, timeout=10)
        print(f"   📡 Response: {schools_response.status_code}")
        
        if schools_response.status_code == 200:
            schools_data = schools_response.json()
            print(f"   ✅ Schools API successful: {len(schools_data.get('results', []))} schools")
        else:
            print(f"   ❌ Schools API failed: {schools_response.status_code}")
            print(f"   Error: {schools_response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Schools API request failed: {str(e)}")
    
    # Test 2: Branches API with access token
    print(f"\n🔍 Test 2: Branches API with Bearer token...")
    
    try:
        branches_response = requests.get('http://localhost:8000/api/schools/branch/', headers=headers, timeout=10)
        print(f"   📡 Response: {branches_response.status_code}")
        
        if branches_response.status_code == 200:
            branches_data = branches_response.json()
            print(f"   ✅ Branches API successful: {len(branches_data.get('results', []))} branches")
        else:
            print(f"   ❌ Branches API failed: {branches_response.status_code}")
            print(f"   Error: {branches_response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Branches API request failed: {str(e)}")
    
    # Test 3: Token refresh
    print(f"\n🔍 Test 3: Token refresh endpoint...")
    
    try:
        refresh_response = requests.post(
            'http://localhost:8000/api/core/auth/token/refresh/',
            json={'refresh': refresh_token},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        print(f"   📡 Response: {refresh_response.status_code}")
        
        if refresh_response.status_code == 200:
            refresh_data = refresh_response.json()
            new_access = refresh_data.get('access')
            print(f"   ✅ Token refresh successful: {new_access[:50] if new_access else 'No access token'}...")
        else:
            print(f"   ❌ Token refresh failed: {refresh_response.status_code}")
            print(f"   Error: {refresh_response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Token refresh request failed: {str(e)}")
    
    print(f"\n📋 Frontend Authentication Instructions:")
    print(f"   1. Open browser console on the frontend")
    print(f"   2. Run: localStorage.setItem('token', '{access_token}')")
    print(f"   3. Run: localStorage.setItem('refreshToken', '{refresh_token}')")
    print(f"   4. Refresh the EditStudent page")
    print(f"   5. The school assignment dropdown should now load properly")
    
    print(f"\n🎯 Frontend authentication test complete!")

if __name__ == "__main__":
    test_frontend_auth()

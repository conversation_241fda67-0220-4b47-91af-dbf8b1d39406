import axiosWithAuth from '../utils/axiosInterceptor';

export interface User {
  id?: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  user_type: 'student' | 'teacher' | 'admin';
  school: number;
  school_branch: number;
  is_active: boolean;
}

export interface SchoolBranch {
  id?: number;
  name: string;
  code: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  registration_number: string;
  established_date: string;
  is_active: boolean;
  school: number;
  users: number[];
}

export interface Student {
  id: number;
  user: User;
  profile_picture?: string;
  date_of_birth?: string;
  gender?: 'M' | 'F';
  school_branch?: SchoolBranch;
  school_branch_id: number;
  class_teacher?: number;
  class_name?: number | string;
  stream?: number | string;
  stream_name?: string;
  admission_number: string;
  current_class?: number;
  subjects?: number[];
  grade?: string;
  admission_date?: string;
  // These fields are for convenience in the UI
  first_name?: string;
  last_name?: string;
}

interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface StudentFormData {
  // User fields
  username?: string;
  email?: string;
  first_name: string;
  last_name: string;
  phone?: string;
  is_active?: boolean;

  // Student personal information
  gender: 'M' | 'F';
  date_of_birth?: string;
  address?: string;
  phone_number?: string;
  emergency_contact?: string;
  additional_notes?: string;

  // Academic information
  admission_number?: string;
  admission_date?: string;
  class_name?: string | number;
  grade?: string;
  stream?: string | number;
  stream_name?: string;
  current_class?: number;
  class_teacher?: number;
  subjects?: number[];
  subject_type?: string;

  // School information
  school_branch_id?: number;
  parent?: number;

  // Documents
  profile_picture?: File | null;
  birth_certificate?: File | null;
  result_slip?: File | null;
  admission_letter?: File | null;
}

// Helper function to map student response
const mapStudentResponse = (student: any) => {
  let streamName = '-';
  if (student.stream_name) {
    streamName = student.stream_name;
  } else if (typeof student.stream === 'object' && student.stream?.name) {
    streamName = student.stream.name;
  }

  return {
    ...student,
    first_name: student.user?.first_name || student.first_name || '',
    last_name: student.user?.last_name || student.last_name || '',
    stream_name: streamName,
  };
};

export const studentService = {
  async getAllStudents(params?: {
    class?: string;
    grade?: string;
    search?: string;
    page?: number;
    page_size?: number;
    school_branch?: number;
    is_active?: boolean;
  }): Promise<PaginatedResponse<Student>> {
    console.log('📡 Making API call to /api/users/students/ with params:', params);

    const response = await axiosWithAuth.get('/api/users/students/', { params });

    console.log('📡 API Response status:', response.status);
    console.log('📡 API Response data type:', typeof response.data);
    console.log('📡 API Response data:', response.data);

    // Check if response is HTML (indicates backend not running or wrong endpoint)
    if (typeof response.data === 'string' && response.data.includes('<!DOCTYPE html>')) {
      console.error('❌ API returned HTML instead of JSON - backend may not be running');
      throw new Error('Backend API not available - received HTML instead of JSON');
    }

    if (!response.data.results) {
      console.warn('API response missing results array:', response.data);
      return {
        count: 0,
        next: null,
        previous: null,
        results: []
      };
    }

    // Map the response to match our Student interface
    const mappedResults = response.data.results.map((student: any, index: number) => {
      console.log(`🔍 Raw student ${index} from API:`, student);
      console.log(`🔍 Student ${index} ID field:`, student.id);
      console.log(`🔍 Student ${index} user.id field:`, student.user?.id);

      // Get stream name if available
      let streamName = '-';
      if (student.stream_name) {
        streamName = student.stream_name;
      } else if (typeof student.stream === 'object' && student.stream?.name) {
        streamName = student.stream.name;
      }

      const mappedStudent = {
        ...student,
        // Ensure we have an ID field - try multiple sources
        id: student.id || student.user?.id || student.pk,
        // Add convenience fields for UI
        first_name: student.user?.first_name || student.first_name || '',
        last_name: student.user?.last_name || student.last_name || '',
        // Add stream_name
        stream_name: streamName,
      };

      console.log(`🔍 Mapped student ${index}:`, mappedStudent);
      console.log(`🔍 Final student ${index} ID:`, mappedStudent.id);

      return mappedStudent;
    });

    return {
      ...response.data,
      results: mappedResults
    };
  },

  async getStudentById(id: number): Promise<Student> {
    const response = await axiosWithAuth.get(`/api/users/students/${id}/`);

    console.log('🔍 getStudentById - Raw API response:', response.data);

    // Map the response to match our Student interface
    const student = response.data;

    // Get stream name if available
    let streamName = '-';
    if (student.stream_name) {
      streamName = student.stream_name;
    } else if (typeof student.stream === 'object' && student.stream?.name) {
      streamName = student.stream.name;
    }

    const mappedStudent = {
      ...student,
      // Ensure we have an ID field - try multiple sources
      id: student.id || student.user?.id || student.pk || id, // Use the requested ID as fallback
      // Add convenience fields for UI
      first_name: student.user?.first_name || student.first_name || '',
      last_name: student.user?.last_name || student.last_name || '',
      // Add stream_name
      stream_name: streamName,
    };

    console.log('🔍 getStudentById - Mapped student:', mappedStudent);
    console.log('🔍 getStudentById - Final student ID:', mappedStudent.id);

    return mappedStudent;
  },

  async createStudent(data: StudentFormData): Promise<Student> {
    // Create FormData for file upload
    const formData = new FormData();

    // Prepare user data
    const userData = {
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email || '',
      phone: data.phone || '',
      username: data.username || '',
      user_type: 'student',
      is_active: data.is_active !== undefined ? data.is_active : true,
    };

    formData.append('user', JSON.stringify(userData));

    // Add all student fields
    const studentFields = ['gender', 'date_of_birth', 'admission_number', 'class_name', 'stream',
                          'school_branch_id', 'class_teacher', 'current_class', 'subjects'];

    studentFields.forEach(field => {
      const value = data[field as keyof StudentFormData];
      if (value !== undefined && field !== 'stream_name') {
        if (Array.isArray(value)) {
          // Handle arrays like subjects
          formData.append(field, JSON.stringify(value));
        } else {
          formData.append(field, String(value));
        }
      }
    });

    // Add profile picture if it exists
    if (data.profile_picture) {
      formData.append('profile_picture', data.profile_picture);
    }

    const response = await axiosWithAuth.post('/api/users/students/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    // Map the response to include stream_name
    const student = response.data;
    let streamName = '-';
    if (student.stream_name) {
      streamName = student.stream_name;
    } else if (typeof student.stream === 'object' && student.stream?.name) {
      streamName = student.stream.name;
    }

    return {
      ...student,
      first_name: student.user?.first_name || student.first_name || '',
      last_name: student.user?.last_name || student.last_name || '',
      stream_name: streamName,
    };
  },

  async updateStudent(id: number, data: Partial<StudentFormData>): Promise<Student> {
    console.log('🔍 UpdateStudent called with ID:', id);
    console.log('🔍 UpdateStudent data:', data);

    // If there are any files, use FormData, otherwise use JSON
    const hasFiles = data.profile_picture || data.birth_certificate || data.result_slip || data.admission_letter;

    if (hasFiles) {
      // Use FormData for file upload
      const formData = new FormData();

      // Prepare user data if it exists - only include non-empty values
      const userData: any = {};

      if (data.first_name && data.first_name.trim() !== '') {
        userData.first_name = data.first_name;
      }
      if (data.last_name && data.last_name.trim() !== '') {
        userData.last_name = data.last_name;
      }
      if (data.email && data.email.trim() !== '') {
        userData.email = data.email;
      }
      if (data.phone && data.phone.trim() !== '') {
        userData.phone = data.phone;
      }
      if (data.is_active !== undefined) {
        userData.is_active = data.is_active;
      }

      // Only append user data if we have any fields to update
      if (Object.keys(userData).length > 0) {
        console.log('🔍 User data to send:', userData);
        formData.append('user', JSON.stringify(userData));
      } else {
        console.log('🔍 No user data to send - all fields empty');
      }

      // Add all student fields (excluding subjects which needs special handling)
      const studentFields = ['gender', 'date_of_birth', 'admission_number', 'class_name', 'stream',
                            'school_branch_id', 'class_teacher', 'current_class'];

      studentFields.forEach(field => {
        const value = data[field as keyof StudentFormData];
        if (value !== undefined && value !== null && value !== '') {
          console.log(`🔍 Adding field ${field}:`, value);
          formData.append(field, String(value));
        }
      });

      // Handle subjects separately with validation
      if (data.subjects && Array.isArray(data.subjects) && data.subjects.length > 0) {
        const validSubjects = data.subjects.filter(subject =>
          typeof subject === 'number' && !isNaN(subject) && subject > 0
        );
        if (validSubjects.length > 0) {
          formData.append('subjects', JSON.stringify(validSubjects));
          console.log('🔍 Adding valid subjects to FormData:', validSubjects);
        } else {
          console.log('🔍 Skipping subjects in FormData - no valid subject IDs');
        }
      } else {
        console.log('🔍 Skipping subjects in FormData - empty or invalid:', data.subjects);
      }

      // Add all file uploads
      if (data.profile_picture) {
        formData.append('profile_picture', data.profile_picture);
      }
      if (data.birth_certificate) {
        formData.append('birth_certificate', data.birth_certificate);
      }
      if (data.result_slip) {
        formData.append('result_slip', data.result_slip);
      }
      if (data.admission_letter) {
        formData.append('admission_letter', data.admission_letter);
      }

      console.log('🔍 Making PATCH request with FormData');
      const response = await axiosWithAuth.patch(`/api/users/students/${id}/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return mapStudentResponse(response.data);
    } else {
      // Use JSON for non-file updates (simpler and more reliable)
      const updateData: any = {};

      // Add user data directly (not nested) - backend expects flat structure
      // Only include fields that have actual values (not empty strings)
      if (data.first_name !== undefined && data.first_name !== '') {
        updateData.first_name = data.first_name;
      }
      if (data.last_name !== undefined && data.last_name !== '') {
        updateData.last_name = data.last_name;
      }
      if (data.email !== undefined && data.email !== '') {
        updateData.email = data.email;
      }
      if (data.phone !== undefined && data.phone !== '') {
        updateData.phone = data.phone;
      }
      if (data.is_active !== undefined) {
        updateData.is_active = data.is_active;
      }

      // Add student personal information - only non-empty values
      if (data.gender && ['M', 'F'].includes(data.gender)) {
        updateData.gender = data.gender;
      }
      if (data.date_of_birth && data.date_of_birth !== '') {
        updateData.date_of_birth = data.date_of_birth;
      }
      if (data.address !== undefined && data.address !== '') {
        updateData.address = data.address;
      }
      if (data.phone_number !== undefined && data.phone_number !== '') {
        updateData.phone_number = data.phone_number;
      }
      if (data.emergency_contact !== undefined && data.emergency_contact !== '') {
        updateData.emergency_contact = data.emergency_contact;
      }
      if (data.additional_notes !== undefined && data.additional_notes !== '') {
        updateData.additional_notes = data.additional_notes;
      }

      // Add academic information - only non-empty values
      if (data.admission_number && data.admission_number !== '') {
        updateData.admission_number = data.admission_number;
      }
      if (data.admission_date && data.admission_date !== '') {
        updateData.admission_date = data.admission_date;
      }
      if (data.class_name !== undefined && data.class_name !== '') {
        updateData.class_name = data.class_name;
      }
      if (data.stream !== undefined && data.stream !== '') {
        updateData.stream = data.stream;
      }
      if (data.current_class !== undefined && data.current_class !== null) {
        updateData.current_class = data.current_class;
      }
      if (data.class_teacher !== undefined && data.class_teacher !== null) {
        updateData.class_teacher = data.class_teacher;
      }
      if (data.subject_type !== undefined && data.subject_type !== '') {
        updateData.subject_type = data.subject_type;
      }
      // Handle subjects field carefully - only include if we have valid subject IDs
      if (data.subjects && Array.isArray(data.subjects) && data.subjects.length > 0) {
        // Only include subjects if they are valid numbers (primary keys)
        const validSubjects = data.subjects.filter(subject =>
          typeof subject === 'number' && !isNaN(subject) && subject > 0
        );
        if (validSubjects.length > 0) {
          updateData.subjects = validSubjects;
        }
        console.log('🔍 Subjects processing:', {
          original: data.subjects,
          valid: validSubjects,
          included: validSubjects.length > 0
        });
      } else {
        // Don't send subjects field at all if it's empty or invalid
        console.log('🔍 Skipping subjects field - empty or invalid:', data.subjects);
      }

      // Add school information - only non-empty values
      if (data.school_branch_id !== undefined && data.school_branch_id !== null) {
        updateData.school_branch_id = data.school_branch_id;
      }
      if (data.parent !== undefined && data.parent !== null) {
        updateData.parent = data.parent;
      }

      console.log('🔍 Making PATCH request with JSON data:', updateData);
      console.log('🔍 Request URL:', `/api/users/students/${id}/`);
      console.log('🔍 Request headers:', { 'Content-Type': 'application/json' });
      console.log('🔍 Original form data received:', data);
      console.log('🔍 Number of fields in updateData:', Object.keys(updateData).length);

      // Debug specific problematic fields
      console.log('🔍 Email field debug:', {
        original: data.email,
        type: typeof data.email,
        length: data.email?.length,
        isEmpty: data.email === '',
        isUndefined: data.email === undefined,
        included: 'email' in updateData
      });
      console.log('🔍 Phone field debug:', {
        original: data.phone,
        type: typeof data.phone,
        length: data.phone?.length,
        isEmpty: data.phone === '',
        isUndefined: data.phone === undefined,
        included: 'phone' in updateData
      });

      try {
        const response = await axiosWithAuth.patch(`/api/users/students/${id}/`, updateData, {
          headers: {
            'Content-Type': 'application/json'
          }
        });
        console.log('✅ Update successful:', response.data);
        return mapStudentResponse(response.data);
      } catch (error: any) {
        console.error('❌ PATCH request failed:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          url: error.config?.url,
          method: error.config?.method,
          sentData: updateData,
          originalFormData: data
        });

        // Log detailed error information
        if (error.response?.data) {
          console.error('❌ Backend Error Response:', JSON.stringify(error.response.data, null, 2));
        }

        throw error;
      }

      return mapStudentResponse(response.data);
    }
  },

  async deleteStudent(id: number): Promise<void> {
    await axiosWithAuth.delete(`/api/users/students/${id}/`);
  },

  async toggleStudentStatus(id: number, isActive: boolean): Promise<Student> {
    // Create a partial update with just the user status
    const data = {
      user: {
        is_active: isActive
      }
    };

    const response = await axiosWithAuth.patch(`/api/users/students/${id}/`, data);
    return response.data;
  },

  async exportToExcel() {
    const response = await axiosWithAuth.get('/api/users/students/export/excel/', {
      responseType: 'blob'
    });
    return response.data;
  },

  async exportToPDF() {
    const response = await axiosWithAuth.get('/api/users/students/export/pdf/', {
      responseType: 'blob'
    });
    return response.data;
  }
};

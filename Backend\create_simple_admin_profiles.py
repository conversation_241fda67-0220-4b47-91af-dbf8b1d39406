#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from core.models import CustomUser
from schools.models import School, SchoolBranch
from users.models import Staff
from academics.models import Department

def create_simple_admin_profiles():
    print("🔧 Creating simple admin user profiles...")
    
    # Get Sample School and Main Campus
    try:
        sample_school = School.objects.get(name="Sample School")
        main_campus = SchoolBranch.objects.get(school=sample_school, name="Main Campus")
        print(f"✅ Target School: {sample_school.name}")
        print(f"✅ Target Branch: {main_campus.name}")
    except (School.DoesNotExist, SchoolBranch.DoesNotExist):
        print("❌ Sample School or Main Campus not found!")
        return
    
    # Get the Administration department
    try:
        admin_dept = Department.objects.get(name="Administration", school=sample_school)
        print(f"✅ Found Administration department: {admin_dept}")
    except Department.DoesNotExist:
        print("❌ Administration department not found!")
        return
    
    # Find admin users without profiles
    admin_users = CustomUser.objects.filter(
        is_staff=True,
        is_active=True
    )
    
    for user in admin_users:
        print(f"\n👤 Processing admin user: {user.username} ({user.email})")
        
        # Check if user already has a staff profile
        try:
            if hasattr(user, 'staff') and user.staff:
                print(f"   - ✅ Already has staff profile: {user.staff.school_branch}")
                continue
        except:
            pass
        
        # Create staff profile with minimal required fields
        try:
            staff_profile = Staff.objects.create(
                user=user,
                school_branch=main_campus,
                staff_number=f"ADMIN{user.id:04d}",
                department=admin_dept,
                role="other",
                phone_number="+254700000000",
                email=user.email,
                date_of_birth="1990-01-01",
                gender="M"
            )
            print(f"   - ✅ Created staff profile: {staff_profile}")
            print(f"   - 🏢 School Branch: {staff_profile.school_branch}")
            print(f"   - 🏛️ Department: {staff_profile.department}")
            print(f"   - 💼 Role: {staff_profile.role}")
            
        except Exception as e:
            print(f"   - ❌ Failed to create staff profile: {e}")
    
    print(f"\n=== VERIFICATION ===")
    # Verify all admin users now have profiles
    for user in admin_users:
        try:
            if hasattr(user, 'staff') and user.staff:
                print(f"✅ {user.username}: {user.staff.school_branch}")
            else:
                print(f"❌ {user.username}: No profile")
        except:
            print(f"❌ {user.username}: No profile")

if __name__ == "__main__":
    create_simple_admin_profiles()

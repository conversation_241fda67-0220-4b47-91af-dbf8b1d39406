import axios from 'axios';
import { authService } from '../services/authService';
import { store } from '../store/store';
import { refreshToken } from '../store/slices/authSlice';

// Create a custom axios instance with backend base URL
const axiosWithAuth = axios.create({
  baseURL: 'http://localhost:8000', // Backend API base URL
});

// Create a flag to prevent multiple refresh attempts
let isRefreshing = false;
// Store pending requests that should be retried after token refresh
let failedQueue: any[] = [];

// Process the queue of failed requests
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

// Add a request interceptor
axiosWithAuth.interceptors.request.use(
  (config) => {
    console.log('🌐 REQUEST:', config.method?.toUpperCase(), config.url);
    // Add token to request headers
    const token = authService.getToken();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔑 Added token to request');
    } else {
      // If no token, check if this is an authenticated endpoint
      const publicEndpoints = [
        '/api/core/auth/login/',
        '/api/core/auth/register/',
        '/api/core/auth/token/refresh/',
        '/api/core/password-reset/'
      ];

      const isPublicEndpoint = publicEndpoints.some(endpoint =>
        config.url?.includes(endpoint)
      );

      if (!isPublicEndpoint) {
        console.log('❌ BLOCKING REQUEST: No token for protected endpoint:', config.url);
        // For non-public endpoints without a token, cancel the request
        // to prevent unnecessary 401 errors

        return Promise.reject({
          response: { status: 401, statusText: 'Unauthorized' },
          message: 'No authentication token available'
        });
      } else {
        console.log('✅ PUBLIC ENDPOINT: Allowing request without token');
      }
    }

    return config;
  },
  (error) => {
    console.log('❌ REQUEST ERROR:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor
axiosWithAuth.interceptors.response.use(
  (response) => {
    console.log('✅ RESPONSE SUCCESS:', response.config.method?.toUpperCase(), response.config.url, response.status);
    return response;
  },
  async (error) => {
    console.error('❌ RESPONSE ERROR:', {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    const originalRequest = error.config;

    // If the error is due to an expired token and we haven't tried to refresh it yet
    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      console.log('🔄 401 ERROR: Attempting token refresh for:', originalRequest.url);
      originalRequest._retry = true;

      // Check if we've already tried to refresh the token for this request
      if (originalRequest._retryCount >= 2) {
        console.log('❌ MAX RETRIES REACHED: Redirecting to signin');
        window.location.href = '/signin';
        return Promise.reject(error);
      }

      // Initialize retry count
      originalRequest._retryCount = originalRequest._retryCount ? originalRequest._retryCount + 1 : 1;
      console.log('🔢 RETRY COUNT:', originalRequest._retryCount);

      // If we're already refreshing, add this request to the queue
      if (isRefreshing) {
        console.log('⏳ ALREADY REFRESHING: Adding to queue');
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then(token => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return axiosWithAuth(originalRequest);
          })
          .catch(err => {
            return Promise.reject(err);
          });
      }

      console.log('🔄 STARTING TOKEN REFRESH...');
      isRefreshing = true;

      try {
        // Dispatch the refresh token action through Redux
        const result = await store.dispatch(refreshToken()).unwrap();
        const newToken = result.access;

        // If token refresh was successful, retry the original request
        if (newToken) {
          console.log('✅ TOKEN REFRESH SUCCESS: Retrying original request');
          // Update the Authorization header with the new token
          originalRequest.headers.Authorization = `Bearer ${newToken}`;

          // Process the queue with the new token
          processQueue(null, newToken);

          return axiosWithAuth(originalRequest);
        }
      } catch (refreshError) {
        console.log('❌ TOKEN REFRESH FAILED:', refreshError);

        // Process the queue with the error
        processQueue(refreshError, null);

        // If token refresh fails, redirect to login
        window.location.href = '/signin';
        return Promise.reject(refreshError);
      } finally {
        console.log('🏁 TOKEN REFRESH COMPLETE');
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

export default axiosWithAuth;

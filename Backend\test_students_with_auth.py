#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from django.contrib.auth import authenticate
from rest_framework.authtoken.models import Token
from users.models import Student
from schools.models import SchoolBranch

def test_students_with_auth():
    print("🧪 Testing Students API with Authentication...")
    
    # Get a user token for testing
    from core.models import CustomUser
    admin_user = CustomUser.objects.filter(is_staff=True).first()
    
    if not admin_user:
        print("❌ No admin user found")
        return
        
    token, created = Token.objects.get_or_create(user=admin_user)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Check students in each branch
    branches = SchoolBranch.objects.all()
    for branch in branches:
        students = Student.objects.filter(school_branch=branch)
        print(f"\n🏢 {branch.school.name} - {branch.name} (ID: {branch.id})")
        print(f"   📊 Students in database: {students.count()}")
        
        for student in students:
            print(f"   👨‍🎓 {student.user.first_name} {student.user.last_name}")
            print(f"      - Admission: {student.admission_number}")
            print(f"      - Active: {student.user.is_active}")
    
    # Test API call with authentication
    import requests
    
    headers = {'Authorization': f'Token {token.key}'}
    
    # Test Sample School - Main Campus (ID: 5)
    print(f"\n🔍 Testing API call for Sample School - Main Campus...")
    response = requests.get(
        'http://localhost:8000/api/users/students/',
        params={'school_branch': 5, 'is_active': True},
        headers=headers,
        timeout=5
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ API Success: {data.get('count', 0)} students returned")
        for student in data.get('results', [])[:3]:
            name = f"{student.get('first_name', '')} {student.get('last_name', '')}"
            print(f"   👨‍🎓 {name}")
    else:
        print(f"❌ API Error: {response.status_code} - {response.text[:200]}")

if __name__ == "__main__":
    test_students_with_auth()

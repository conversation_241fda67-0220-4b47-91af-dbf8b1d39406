#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_inactive_filtering():
    print("🧪 Testing Inactive Student Filtering...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Get a student to test with
    student = Student.objects.first()
    if not student:
        print("❌ No students found")
        return
    
    print(f"\n📋 Testing with student: {student.user.first_name} {student.user.last_name}")
    print(f"   - Original status: {'Active' if student.user.is_active else 'Inactive'}")
    
    headers = {'Authorization': f'Bearer {access_token}'}
    
    # 1. Test with active filter
    print(f"\n🔍 Testing active students filter...")
    active_url = 'http://localhost:8000/api/users/students/'
    active_params = {'is_active': 'true', 'page_size': 100}
    
    active_response = requests.get(active_url, headers=headers, params=active_params, timeout=10)
    if active_response.status_code == 200:
        active_data = active_response.json()
        active_count = len(active_data.get('results', []))
        print(f"   ✅ Found {active_count} active students")
    else:
        print(f"   ❌ Active filter failed: {active_response.status_code}")
    
    # 2. Test with inactive filter
    print(f"\n🔍 Testing inactive students filter...")
    inactive_url = 'http://localhost:8000/api/users/students/'
    inactive_params = {'is_active': 'false', 'page_size': 100}
    
    inactive_response = requests.get(inactive_url, headers=headers, params=inactive_params, timeout=10)
    if inactive_response.status_code == 200:
        inactive_data = inactive_response.json()
        inactive_count = len(inactive_data.get('results', []))
        print(f"   ✅ Found {inactive_count} inactive students")
        
        if inactive_count > 0:
            print(f"   📋 Sample inactive students:")
            for i, student in enumerate(inactive_data.get('results', [])[:3]):
                user = student.get('user', {})
                print(f"      {i+1}. {user.get('first_name')} {user.get('last_name')} - Active: {user.get('is_active')}")
    else:
        print(f"   ❌ Inactive filter failed: {inactive_response.status_code}")
    
    # 3. Test without filter (should return all)
    print(f"\n🔍 Testing no filter (all students)...")
    all_url = 'http://localhost:8000/api/users/students/'
    all_params = {'page_size': 100}
    
    all_response = requests.get(all_url, headers=headers, params=all_params, timeout=10)
    if all_response.status_code == 200:
        all_data = all_response.json()
        all_count = len(all_data.get('results', []))
        print(f"   ✅ Found {all_count} total students")
        
        # Verify the math
        if 'active_count' in locals() and 'inactive_count' in locals():
            expected_total = active_count + inactive_count
            if all_count >= expected_total:
                print(f"   ✅ Math checks out: {active_count} active + {inactive_count} inactive ≤ {all_count} total")
            else:
                print(f"   ⚠️ Math doesn't add up: {active_count} active + {inactive_count} inactive > {all_count} total")
    else:
        print(f"   ❌ All students query failed: {all_response.status_code}")
    
    print(f"\n🎯 Filtering test complete!")

if __name__ == "__main__":
    test_inactive_filtering()

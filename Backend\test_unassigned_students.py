#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student, SchoolBranch
from core.models import CustomUser

def test_unassigned_students():
    print("🔍 Testing Unassigned Students Analysis...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Analyze current student assignments
    print(f"\n📊 Current Student Assignment Analysis:")
    
    total_students = Student.objects.count()
    print(f"   - Total students: {total_students}")
    
    # Students with complete assignments (school + branch)
    complete_assignments = Student.objects.filter(
        school_branch__isnull=False,
        school_branch__school__isnull=False
    ).count()
    print(f"   - Students with complete assignments: {complete_assignments}")
    
    # Students with no branch assignment
    no_branch = Student.objects.filter(school_branch__isnull=True).count()
    print(f"   - Students with no branch: {no_branch}")
    
    # Students with branch but no school (shouldn't happen in normal cases)
    no_school = Student.objects.filter(
        school_branch__isnull=False,
        school_branch__school__isnull=True
    ).count()
    print(f"   - Students with branch but no school: {no_school}")
    
    # Show detailed breakdown
    print(f"\n📋 Detailed Breakdown:")
    
    # Students without branches
    if no_branch > 0:
        print(f"   📌 Students without branch assignments:")
        unassigned_students = Student.objects.filter(school_branch__isnull=True)[:5]
        for i, student in enumerate(unassigned_students):
            print(f"      {i+1}. {student.user.first_name} {student.user.last_name} ({student.admission_number})")
        if no_branch > 5:
            print(f"      ... and {no_branch - 5} more")
    
    # Students by branch
    print(f"\n   📌 Students by branch:")
    branches = SchoolBranch.objects.all()
    for branch in branches:
        count = Student.objects.filter(school_branch=branch).count()
        if count > 0:
            school_name = branch.school.name if branch.school else "No School"
            print(f"      - {branch.name} ({school_name}): {count} students")
    
    # Test API call to get all students (simulating frontend behavior)
    print(f"\n📡 Testing API Response:")
    headers = {'Authorization': f'Bearer {access_token}'}
    url = 'http://localhost:8000/api/users/students/'
    params = {'page_size': 1000}
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            api_students = data.get('results', [])
            print(f"   ✅ API returned {len(api_students)} students")
            
            # Analyze API response for assignments
            api_no_school = 0
            api_no_branch = 0
            api_no_school_no_branch = 0
            api_complete = 0
            
            for student in api_students:
                school_branch = student.get('school_branch')
                
                if not school_branch:
                    api_no_school_no_branch += 1
                else:
                    # Handle both dict and int cases for school_branch
                    if isinstance(school_branch, dict):
                        school_obj = school_branch.get('school')
                        has_school = school_obj.get('id') if isinstance(school_obj, dict) and school_obj else None
                        has_branch = school_branch.get('id')
                    else:
                        # If school_branch is just an ID, we assume it's assigned
                        has_school = True
                        has_branch = True

                    if not has_school and not has_branch:
                        api_no_school_no_branch += 1
                    elif not has_school:
                        api_no_school += 1
                    elif not has_branch:
                        api_no_branch += 1
                    else:
                        api_complete += 1
            
            print(f"   📊 API Analysis Results:")
            print(f"      - Complete assignments: {api_complete}")
            print(f"      - No school & no branch: {api_no_school_no_branch}")
            print(f"      - No school only: {api_no_school}")
            print(f"      - No branch only: {api_no_branch}")
            
            total_issues = api_no_school + api_no_branch + api_no_school_no_branch
            print(f"      - Total assignment issues: {total_issues}")
            
            if total_issues > 0:
                print(f"\n   🎯 UnassignedStudents page should show {total_issues} students with assignment issues")
            else:
                print(f"\n   ✅ All students are properly assigned!")
                
        else:
            print(f"   ❌ API request failed: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Request failed: {str(e)}")
    
    print(f"\n🎯 Analysis complete!")

if __name__ == "__main__":
    test_unassigned_students()

import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import { studentService, type Student } from "../../services/studentService";
import Button from "../../components/ui/button/Button";

export default function ViewStudent() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [student, setStudent] = useState<Student | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStudent = async () => {
      if (!id) return;

      try {
        setLoading(true);
        setError(null);
        const data = await studentService.getStudentById(parseInt(id));
        console.log('🔍 ViewStudent - Received student data:', data);
        console.log('🔍 ViewStudent - User object:', data.user);
        console.log('🔍 ViewStudent - User is_active:', data.user?.is_active);
        console.log('🔍 ViewStudent - User is_active type:', typeof data.user?.is_active);
        setStudent(data);
      } catch (err: unknown) {
        const error = err as { response?: { data?: { message?: string } }, message?: string };
        console.error('Error:', error.response?.data || error.message);
        setError(error.response?.data?.message || "Failed to fetch student details");
      } finally {
        setLoading(false);
      }
    };

    fetchStudent();
  }, [id]);

  const handleDelete = async () => {
    if (!id || !student) return;

    if (window.confirm(`Are you sure you want to delete ${student.first_name} ${student.last_name}?`)) {
      try {
        await studentService.deleteStudent(parseInt(id));
        navigate('/AllStudents');
      } catch (err: unknown) {
        const error = err as { response?: { data?: { message?: string } }, message?: string };
        console.error('Error:', error.response?.data || error.message);
        alert(`Failed to delete student: ${error.response?.data?.message || error.message || 'Unknown error'}`);
      }
    }
  };

  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  const handleToggleStatus = async () => {
    if (!id || !student || isUpdatingStatus) return;

    const newStatus = student.user?.is_active !== true;
    const action = newStatus ? 'activate' : 'reactivate';
    const actionPast = newStatus ? 'activated' : 'deactivated';

    // Enhanced confirmation dialog
    const confirmMessage = newStatus
      ? `🔄 Reactivate Student\n\nAre you sure you want to reactivate ${student.first_name} ${student.last_name}?\n\nThis will:\n• Restore access to the system\n• Make the student visible in active lists\n• Allow login and participation in activities`
      : `⚠️ Deactivate Student\n\nAre you sure you want to deactivate ${student.first_name} ${student.last_name}?\n\nThis will:\n• Remove access to the system\n• Hide from active student lists\n• Prevent login and participation`;

    if (window.confirm(confirmMessage)) {
      try {
        setIsUpdatingStatus(true);
        console.log(`🔄 ${action}ing student ${student.first_name} ${student.last_name}...`);

        await studentService.toggleStudentStatus(parseInt(id), newStatus);

        // Refresh student data to get updated status
        const updatedStudent = await studentService.getStudentById(parseInt(id));
        setStudent(updatedStudent);

        // Show success message
        const successMessage = `✅ Student ${actionPast} successfully!\n\n${student.first_name} ${student.last_name} is now ${newStatus ? 'active' : 'inactive'}.`;
        alert(successMessage);

        console.log(`✅ Student ${actionPast} successfully`);
      } catch (err: unknown) {
        const error = err as { response?: { data?: { message?: string } }, message?: string };
        console.error(`❌ Error ${action}ing student:`, error.response?.data || error.message);

        const errorMessage = `❌ Failed to ${action} student\n\n${error.response?.data?.message || error.message || 'Unknown error occurred'}`;
        alert(errorMessage);
      } finally {
        setIsUpdatingStatus(false);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
        <p className="ml-4">Loading student details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-600 p-4 rounded">
        <h3 className="font-medium">Error loading student details</h3>
        <p>{error}</p>
        <button
          type="button"
          onClick={() => navigate('/AllStudents')}
          className="mt-2 text-sm text-red-800 underline"
        >
          Back to Students List
        </button>
      </div>
    );
  }

  if (!student) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-600 p-4 rounded">
        <h3 className="font-medium">Student not found</h3>
        <p>The requested student could not be found.</p>
        <button
          type="button"
          onClick={() => navigate('/AllStudents')}
          className="mt-2 text-sm text-yellow-800 underline"
        >
          Back to Students List
        </button>
      </div>
    );
  }

  return (
    <>
      <PageMeta title={`${student.first_name} ${student.last_name} | ShuleXcel`} description="Student details." />
      <PageBreadcrumb pageTitle="Student Details" />

      <div>
        <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03] p-6">
        {/* Header & Actions */}
        <div className="flex justify-between items-center mb-6">
          <h3 className="font-semibold text-gray-800 text-theme-xl dark:text-white/90 sm:text-2xl">
            Student Information
          </h3>
          <div className="flex flex-wrap gap-3">
            <Button
              variant="secondary"
              onClick={() => navigate('/AllStudents')}
              className="flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to List
            </Button>

            <Button
              variant="primary"
              onClick={() => {
                const studentId = student.id || student.user?.id || id;
                console.log('🔍 ViewStudent - Edit button clicked');
                console.log('🔍 ViewStudent - Final ID for navigation:', studentId);

                if (studentId) {
                  navigate(`/edit-student/${studentId}`);
                } else {
                  console.error('❌ No valid student ID found for edit navigation');
                  alert('Error: Cannot edit student - no valid ID found');
                }
              }}
              className="flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit Student
            </Button>

            <Button
              variant="outline"
              onClick={handleToggleStatus}
              disabled={isUpdatingStatus}
              className={`flex items-center gap-2 ${
                student.user?.is_active === true
                  ? "text-orange-600 border-orange-200 hover:bg-orange-50"
                  : "text-green-600 border-green-200 hover:bg-green-50"
              } ${isUpdatingStatus ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isUpdatingStatus ? (
                <>
                  <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-r-transparent"></div>
                  Updating...
                </>
              ) : student.user?.is_active === true ? (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                  </svg>
                  Deactivate
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Reactivate
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={handleDelete}
              className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Delete
            </Button>
          </div>
        </div>

        {/* Student Profile */}
        <div className="flex flex-col md:flex-row gap-8 mb-8">
          <div className="w-full md:w-1/3 lg:w-1/4">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 flex flex-col items-center">
              <div className="relative w-40 h-40 overflow-hidden rounded-full mb-4 group">
                <img
                  src={student.profile_picture || "/images/user/default-avatar.png"}
                  alt={`${student.first_name} ${student.last_name}`}
                  className="object-cover w-full h-full transition-all duration-200 group-hover:scale-105"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(student.first_name + ' ' + student.last_name)}&size=160&background=3b82f6&color=ffffff&bold=true`;
                  }}
                />
                {/* Profile Picture Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                </div>
                {/* Status Indicator */}
                <div className={`absolute bottom-2 right-2 w-6 h-6 rounded-full border-2 border-white ${
                  student.user?.is_active === true ? 'bg-green-500' : 'bg-red-500'
                }`} title={student.user?.is_active === true ? 'Active Student' : 'Inactive Student'}>
                </div>
              </div>
              <h4 className="text-xl font-semibold text-gray-800 dark:text-white/90">
                {student.first_name} {student.last_name}
              </h4>
              <p className="text-gray-500 dark:text-gray-400 mb-2">
                {student.admission_number}
              </p>
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mb-3">
                📚 {student.grade} - {student.class_name}
              </div>

              {/* Enhanced Status Display */}
              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                student.user?.is_active === true
                  ? 'bg-green-100 text-green-800 border border-green-200'
                  : 'bg-red-100 text-red-800 border border-red-200'
              }`}>
                {student.user?.is_active === true ? (
                  <>
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Active Student</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clipRule="evenodd" />
                    </svg>
                    <span>Inactive Student</span>
                  </>
                )}
              </div>

              {/* Status Description */}
              <div className="mt-2 text-xs text-gray-500 text-center">
                {student.user?.is_active === true
                  ? "Student has full access to the system"
                  : "Student access has been suspended"
                }
              </div>
            </div>
          </div>

          <div className="w-full md:w-2/3 lg:w-3/4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                <h5 className="font-medium text-gray-700 dark:text-white/80 mb-3">👤 Personal Information</h5>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Full Name</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">{student.first_name} {student.last_name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Gender</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {student.gender === 'M' ? '👨 Male' : student.gender === 'F' ? '👩 Female' : '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Date of Birth</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Email</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {student.user?.email || '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Phone</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).phone_number || student.user?.phone || '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Nationality</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).nationality || '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Religion</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).religion || '-'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                <h5 className="font-medium text-gray-700 dark:text-white/80 mb-3">🎓 Academic Information</h5>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Admission Number</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">{student.admission_number}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Admission Date</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).admission_date ? new Date((student as any).admission_date).toLocaleDateString() : '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Grade</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">{student.grade}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Class</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">{student.class_name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Stream</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">{(student as any).stream || '-'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Subject Type</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">{(student as any).subject_type || '-'}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Information Sections - 3 Column Grid */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Guardian Information */}
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h5 className="font-medium text-gray-700 dark:text-white/80 mb-3">👨‍👩‍👧‍👦 Guardian Information</h5>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Guardian Name</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).guardian_name || '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Contact</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).guardian_contact || '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Email</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).guardian_email || '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Relationship</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).guardian_relationship || '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Occupation</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).guardian_occupation || '-'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Medical Information */}
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                <h5 className="font-medium text-gray-700 dark:text-white/80 mb-3">🏥 Medical Information</h5>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Blood Type</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).blood_type ? `🩸 ${(student as any).blood_type}` : '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Emergency Contact</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {(student as any).emergency_contact || '-'}
                    </span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-500 dark:text-gray-400 block mb-2">Medical Conditions</span>
                    <span className="font-medium text-gray-800 dark:text-white/90 text-sm">
                      {(student as any).medical_conditions || 'No known medical conditions'}
                    </span>
                  </div>
                </div>
              </div>

              {/* School Assignment */}
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h5 className="font-medium text-gray-700 dark:text-white/80 mb-3">🏫 School Assignment</h5>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">School</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {student.school_branch?.school?.name || '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Branch</span>
                    <span className="font-medium text-gray-800 dark:text-white/90">
                      {student.school_branch?.name || '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Address</span>
                    <span className="font-medium text-gray-800 dark:text-white/90 text-sm">
                      {(student as any).address || '-'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Summary Section */}
            <div className="mt-6 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
              <h5 className="font-medium text-gray-700 dark:text-white/80 mb-3">📊 Performance Summary</h5>
              <p className="text-gray-500 dark:text-gray-400">
                Performance data will be displayed here. This section can be expanded to include charts, grades, and other academic metrics.
              </p>
              <div className="mt-4">
                <Button
                  variant="secondary"
                  onClick={() => navigate(`/performance-analytics?student=${student.id}`)}
                >
                  View Full Performance
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </>
  );
}
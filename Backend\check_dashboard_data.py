#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from core.models import CustomUser
from schools.models import School, SchoolBranch
from users.models import Student, Teacher, Staff, Parent

def check_dashboard_data():
    print("=== DASHBOARD DATA ANALYSIS ===")

    # Basic counts
    total_users = CustomUser.objects.count()
    students_users = CustomUser.objects.filter(user_type='student').count()
    teachers_users = CustomUser.objects.filter(user_type='teacher').count()
    schools = School.objects.count()
    branches = SchoolBranch.objects.count()

    # Profile counts
    student_profiles = Student.objects.count()
    teacher_profiles = Teacher.objects.count()
    staff_profiles = Staff.objects.count()
    parent_profiles = Parent.objects.count()

    print(f"📊 Total users: {total_users}")
    print(f"👨‍🎓 Student users: {students_users} | Student profiles: {student_profiles}")
    print(f"👨‍🏫 Teacher users: {teachers_users} | Teacher profiles: {teacher_profiles}")
    print(f"👥 Staff profiles: {staff_profiles}")
    print(f"👨‍👩‍👧‍👦 Parent profiles: {parent_profiles}")
    print(f"🏫 Schools: {schools}")
    print(f"🏢 Branches: {branches}")

    # Check school-branch relationships through profiles
    students_with_branch = Student.objects.filter(school_branch__isnull=False).count()
    teachers_with_branch = Teacher.objects.filter(school_branch__isnull=False).count()
    staff_with_branch = Staff.objects.filter(school_branch__isnull=False).count()

    print(f"\n=== SCHOOL-BRANCH RELATIONSHIPS ===")
    print(f"✅ Students with school_branch: {students_with_branch}")
    print(f"✅ Teachers with school_branch: {teachers_with_branch}")
    print(f"✅ Staff with school_branch: {staff_with_branch}")

    # Show sample data for each branch
    print(f"\n=== BRANCH BREAKDOWN ===")
    for branch in SchoolBranch.objects.all():
        branch_students = Student.objects.filter(school_branch=branch).count()
        branch_teachers = Teacher.objects.filter(school_branch=branch).count()
        branch_staff = Staff.objects.filter(school_branch=branch).count()
        total_in_branch = branch_students + branch_teachers + branch_staff
        print(f"🏢 {branch.school.name} - {branch.name}:")
        print(f"   Students: {branch_students}, Teachers: {branch_teachers}, Staff: {branch_staff}, Total: {total_in_branch}")

    # Check for users without profiles
    users_without_profiles = []
    for user in CustomUser.objects.filter(user_type='student'):
        if not hasattr(user, 'student'):
            users_without_profiles.append(f"Student: {user.username}")

    for user in CustomUser.objects.filter(user_type='teacher'):
        if not hasattr(user, 'teacher'):
            users_without_profiles.append(f"Teacher: {user.username}")

    if users_without_profiles:
        print(f"\n=== USERS WITHOUT PROFILES ===")
        for user_info in users_without_profiles[:5]:
            print(f"- {user_info}")
        if len(users_without_profiles) > 5:
            print(f"... and {len(users_without_profiles) - 5} more")

if __name__ == "__main__":
    check_dashboard_data()

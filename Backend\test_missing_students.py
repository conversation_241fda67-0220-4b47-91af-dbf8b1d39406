#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_missing_students():
    print("🔍 Investigating Missing Students...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # 1. Check database directly
    print(f"\n📊 Database Analysis:")
    total_students_db = Student.objects.count()
    active_students_db = Student.objects.filter(user__is_active=True).count()
    inactive_students_db = Student.objects.filter(user__is_active=False).count()
    
    print(f"   - Total students in DB: {total_students_db}")
    print(f"   - Active students in DB: {active_students_db}")
    print(f"   - Inactive students in DB: {inactive_students_db}")
    print(f"   - Math check: {active_students_db} + {inactive_students_db} = {active_students_db + inactive_students_db} (should equal {total_students_db})")
    
    # Check for students without users
    students_without_users = Student.objects.filter(user__isnull=True).count()
    print(f"   - Students without user accounts: {students_without_users}")
    
    # Check for students with null is_active
    students_null_active = Student.objects.filter(user__is_active__isnull=True).count()
    print(f"   - Students with null is_active: {students_null_active}")
    
    headers = {'Authorization': f'Bearer {access_token}'}
    
    # 2. Test API responses
    print(f"\n📡 API Analysis:")
    
    # Test all students (no filter)
    all_url = 'http://localhost:8000/api/users/students/'
    all_params = {'page_size': 1000}  # Large page size to get all
    
    all_response = requests.get(all_url, headers=headers, params=all_params, timeout=10)
    if all_response.status_code == 200:
        all_data = all_response.json()
        api_total = all_data.get('count', 0)
        api_results = len(all_data.get('results', []))
        print(f"   - API total count: {api_total}")
        print(f"   - API results returned: {api_results}")
        
        if api_total != total_students_db:
            print(f"   ⚠️ MISMATCH: API count ({api_total}) != DB count ({total_students_db})")
        
        # Analyze the returned students
        api_active = 0
        api_inactive = 0
        students_with_issues = []
        
        for student in all_data.get('results', []):
            user = student.get('user')
            if not user:
                students_with_issues.append(f"Student ID {student.get('id', 'unknown')} has no user object")
                continue
                
            if user.get('is_active') is True:
                api_active += 1
            elif user.get('is_active') is False:
                api_inactive += 1
            else:
                students_with_issues.append(f"Student {user.get('first_name')} {user.get('last_name')} has is_active = {user.get('is_active')}")
        
        print(f"   - API active students: {api_active}")
        print(f"   - API inactive students: {api_inactive}")
        print(f"   - API students with issues: {len(students_with_issues)}")
        
        if students_with_issues:
            print(f"   📋 Issues found:")
            for issue in students_with_issues[:5]:  # Show first 5 issues
                print(f"      - {issue}")
    else:
        print(f"   ❌ API request failed: {all_response.status_code}")
    
    # 3. Test filtered requests
    print(f"\n🔍 Testing Filtered Requests:")
    
    # Test active filter
    active_params = {'is_active': 'true', 'page_size': 1000}
    active_response = requests.get(all_url, headers=headers, params=active_params, timeout=10)
    if active_response.status_code == 200:
        active_data = active_response.json()
        api_active_filtered = active_data.get('count', 0)
        print(f"   - Active filter API count: {api_active_filtered}")
    else:
        print(f"   ❌ Active filter failed: {active_response.status_code}")
    
    # Test inactive filter
    inactive_params = {'is_active': 'false', 'page_size': 1000}
    inactive_response = requests.get(all_url, headers=headers, params=inactive_params, timeout=10)
    if inactive_response.status_code == 200:
        inactive_data = inactive_response.json()
        api_inactive_filtered = inactive_data.get('count', 0)
        print(f"   - Inactive filter API count: {api_inactive_filtered}")
    else:
        print(f"   ❌ Inactive filter failed: {inactive_response.status_code}")
    
    # 4. Check for permission issues
    print(f"\n🔐 Permission Analysis:")
    
    # Check if there are students in different branches
    from users.models import SchoolBranch
    branches = SchoolBranch.objects.all()
    print(f"   - Total school branches: {branches.count()}")
    
    for branch in branches:
        branch_students = Student.objects.filter(school_branch=branch).count()
        if branch_students > 0:
            print(f"   - Branch '{branch.name}': {branch_students} students")
    
    # Check students without branch
    no_branch_students = Student.objects.filter(school_branch__isnull=True).count()
    print(f"   - Students without branch: {no_branch_students}")
    
    print(f"\n🎯 Analysis complete!")

if __name__ == "__main__":
    test_missing_students()

#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_alice_johnson_debug():
    print("🔍 Debugging Alice Johnson Status...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Find <PERSON> Johnson specifically (handle multiple users)
    try:
        alice_users = CustomUser.objects.filter(first_name="<PERSON>", last_name="<PERSON>")
        print(f"\n📋 Found {alice_users.count()} users named <PERSON>:")

        for i, user in enumerate(alice_users):
            print(f"   {i+1}. User ID: {user.id}, Active: {user.is_active}, Email: {user.email}")

        # Use the first Alice Johnson for testing
        alice_user = alice_users.first()
        alice_student = Student.objects.get(user=alice_user)
        
        print(f"\n📋 Alice Johnson Database Status:")
        print(f"   - User ID: {alice_user.id}")
        print(f"   - Database is_active: {alice_user.is_active}")
        print(f"   - Database is_active type: {type(alice_user.is_active)}")
        print(f"   - Admission Number: {alice_student.admission_number}")
        
        # Test both API endpoints
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # 1. Test getAllStudents with inactive filter
        print(f"\n🔍 Testing getAllStudents API (inactive filter)...")
        list_url = 'http://localhost:8000/api/users/students/'
        list_params = {'is_active': 'false', 'page_size': 100}
        
        list_response = requests.get(list_url, headers=headers, params=list_params, timeout=10)
        if list_response.status_code == 200:
            list_data = list_response.json()
            alice_in_list = None
            for student in list_data.get('results', []):
                if (student.get('user', {}).get('first_name') == 'Alice' and 
                    student.get('user', {}).get('last_name') == 'Johnson'):
                    alice_in_list = student
                    break
            
            if alice_in_list:
                print(f"   ✅ Alice found in inactive students list")
                print(f"   - List API user.is_active: {alice_in_list.get('user', {}).get('is_active')}")
            else:
                print(f"   ❌ Alice NOT found in inactive students list")
        else:
            print(f"   ❌ List API failed: {list_response.status_code}")
        
        # 2. Test getStudentById
        print(f"\n🔍 Testing getStudentById API...")
        detail_url = f'http://localhost:8000/api/users/students/{alice_user.id}/'
        
        detail_response = requests.get(detail_url, headers=headers, timeout=10)
        if detail_response.status_code == 200:
            detail_data = detail_response.json()
            print(f"   ✅ Detail API successful")
            print(f"   - Detail API user.is_active: {detail_data.get('user', {}).get('is_active')}")
            print(f"   - Detail API has user object: {'user' in detail_data}")
            
            if 'user' in detail_data and detail_data['user']:
                user_obj = detail_data['user']
                print(f"   - User object keys: {list(user_obj.keys())}")
                print(f"   - User is_active exists: {'is_active' in user_obj}")
                if 'is_active' in user_obj:
                    print(f"   - User is_active value: {user_obj['is_active']}")
                    print(f"   - User is_active type: {type(user_obj['is_active'])}")
            
            print(f"\n📄 Full Detail API Response:")
            print(json.dumps(detail_data, indent=2, default=str))
        else:
            print(f"   ❌ Detail API failed: {detail_response.status_code}")
            
    except CustomUser.DoesNotExist:
        print("❌ Alice Johnson user not found in database")
    except Student.DoesNotExist:
        print("❌ Alice Johnson student record not found in database")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_alice_johnson_debug()

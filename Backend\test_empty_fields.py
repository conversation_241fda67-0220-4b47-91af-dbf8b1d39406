#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_empty_fields():
    print("🧪 Testing Empty Fields Handling...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Get a student to test with
    student = Student.objects.first()
    if not student:
        print("❌ No students found")
        return
        
    print(f"\n📋 Testing empty fields for: {student.user.first_name} {student.user.last_name}")
    
    # Test data with empty strings (should be filtered out)
    test_data = {
        'first_name': 'Valid<PERSON><PERSON>',  # Valid
        'last_name': '',            # Empty - should be filtered
        'email': '',                # Empty - should be filtered
        'phone': '',                # Empty - should be filtered
        'address': '',              # Empty - should be filtered
        'phone_number': '',         # Empty - should be filtered
        'emergency_contact': '',    # Empty - should be filtered
        'additional_notes': '',     # Empty - should be filtered
        'admission_date': '',       # Empty - should be filtered
        'subject_type': '',         # Empty - should be filtered
    }
    
    print(f"\n🔄 Sending data with empty fields...")
    print(f"   - Valid fields: first_name")
    print(f"   - Empty fields: last_name, email, phone, address, etc.")
    
    # Make API request
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    url = f'http://localhost:8000/api/users/students/{student.user.id}/'
    
    try:
        response = requests.patch(url, json=test_data, headers=headers, timeout=10)
        
        print(f"\n📡 API Response:")
        print(f"   - Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Empty fields handled successfully!")
            
            # Refresh from database
            student.refresh_from_db()
            student.user.refresh_from_db()
            
            print(f"\n🔍 Verification:")
            print(f"   - Name updated: {student.user.first_name}")
            print(f"   - Email unchanged: {student.user.email}")
            print(f"   - Phone unchanged: {student.user.phone}")
            
            if student.user.first_name == 'ValidName':
                print("✅ Valid field was updated")
            else:
                print("❌ Valid field was not updated")
                
        else:
            print(f"❌ Update failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Error text: {response.text[:200]}")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")

if __name__ == "__main__":
    test_empty_fields()

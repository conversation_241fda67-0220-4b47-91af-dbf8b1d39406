#!/usr/bin/env python
import os
import sys
import django
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from schools.models import School, SchoolBranch

def test_dashboard_apis():
    print("🧪 Testing Dashboard APIs...")
    
    # Get Sample School data
    sample_school = School.objects.get(name="Sample School")
    main_campus = SchoolBranch.objects.get(school=sample_school, name="Main Campus")
    
    base_url = "http://localhost:8000/api"
    
    # Test endpoints (without authentication for now)
    endpoints = [
        f"/core/users/counts/?school={sample_school.id}&branch={main_campus.id}",
        f"/core/dashboard/metrics/?school={sample_school.id}&branch={main_campus.id}",
        f"/academics/metrics/?school={sample_school.id}&branch={main_campus.id}",
        f"/statistics/statistics/school/?school_id={sample_school.id}",
        f"/communication/announcements/current/?school={sample_school.id}&branch={main_campus.id}",
        f"/communication/events/upcoming/?school={sample_school.id}&branch={main_campus.id}",
    ]
    
    print(f"🎯 Testing with School: {sample_school.name} (ID: {sample_school.id})")
    print(f"🎯 Testing with Branch: {main_campus.name} (ID: {main_campus.id})")
    print()
    
    for endpoint in endpoints:
        try:
            url = base_url + endpoint
            print(f"🔍 Testing: {endpoint}")
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ SUCCESS: {response.status_code}")
                data = response.json()
                if isinstance(data, dict) and data:
                    print(f"📊 Data keys: {list(data.keys())}")
                elif isinstance(data, list):
                    print(f"📊 List with {len(data)} items")
                else:
                    print(f"📊 Data: {data}")
            elif response.status_code == 401:
                print(f"🔐 AUTHENTICATION REQUIRED: {response.status_code}")
            elif response.status_code == 404:
                print(f"❌ NOT FOUND: {response.status_code}")
            else:
                print(f"⚠️ ERROR: {response.status_code} - {response.text[:100]}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ CONNECTION ERROR: {str(e)}")
        
        print()

if __name__ == "__main__":
    test_dashboard_apis()

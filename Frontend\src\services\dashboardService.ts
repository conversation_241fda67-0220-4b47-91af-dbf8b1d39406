import axios from 'axios';
import { API_URL } from '../config';
import userService from './userService';
import axiosInstance from '../components/utils/AxiosInstance';

// Define types for dashboard data
interface DashboardStats {
  totalStudents: number;
  totalTeachers: number;
  totalClasses: number;
  totalCourses: number;
  totalStreams: number;
  totalDepartments: number;
  totalEvents: number;
  totalAnnouncements: number;
}

interface Announcement {
  id: number;
  title: string;
  date: string;
  priority: string;
}

interface Event {
  id: number;
  title: string;
  date: string;
  location: string;
}

interface Activity {
  id: number;
  user: string;
  action: string;
  timestamp: string;
}

interface DashboardData {
  stats: DashboardStats;
  recentAnnouncements: Announcement[];
  upcomingEvents: Event[];
  recentActivities: Activity[] | undefined;
}

interface DashboardParams {
  schoolId?: number;
  branchId?: number;
}

// Default dashboard data for when API calls fail
const defaultDashboardData: DashboardData = {
  stats: {
    totalStudents: 0,
    totalTeachers: 0,
    totalClasses: 0,
    totalCourses: 0,
    totalStreams: 0,
    totalDepartments: 0,
    totalEvents: 0,
    totalAnnouncements: 0
  },
  recentAnnouncements: [],
  upcomingEvents: [],
  recentActivities: []
};

/**
 * Fetches real dashboard metrics from the core API
 */
const fetchCoreMetrics = async (params?: DashboardParams) => {
  try {
    console.log('🔍 Fetching core metrics...');
    const queryParams = new URLSearchParams();
    if (params?.schoolId) queryParams.append('school', params.schoolId.toString());
    if (params?.branchId) queryParams.append('branch', params.branchId.toString());

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

    const response = await axiosInstance.get(`/core/dashboard/metrics/${queryString}`);
    console.log('✅ Core metrics fetched successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.warn('❌ Core metrics API failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    return null;
  }
};

/**
 * Fetches real dashboard metrics from the academics API
 */
const fetchAcademicMetrics = async (params?: DashboardParams) => {
  try {
    console.log('🔍 Fetching academic metrics...');
    const queryParams = new URLSearchParams();
    if (params?.schoolId) queryParams.append('school', params.schoolId.toString());
    if (params?.branchId) queryParams.append('branch', params.branchId.toString());

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

    // Try different possible endpoints
    const endpoints = [
      `/analytics/metrics/${queryString}`,
      `/dashboard/metrics/${queryString}`,
      `/academics/metrics/${queryString}`
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await axiosInstance.get(endpoint);
        console.log(`✅ Academic metrics fetched successfully from ${endpoint}:`, response.data);
        return response.data;
      } catch (endpointError: any) {
        console.log(`❌ Failed to fetch from ${endpoint}: ${endpointError.response?.status}`);
        continue;
      }
    }

    console.warn('❌ All academic metrics endpoints failed');
    return null;
  } catch (error: any) {
    console.warn('❌ Academic metrics API failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    return null;
  }
};

/**
 * Fetches real announcements from the communication API
 */
const fetchRecentAnnouncements = async (params?: DashboardParams) => {
  try {
    console.log('📢 Fetching recent announcements...');
    const queryParams = new URLSearchParams();
    if (params?.schoolId) queryParams.append('school', params.schoolId.toString());
    if (params?.branchId) queryParams.append('branch', params.branchId.toString());
    queryParams.append('limit', '5'); // Get latest 5 announcements

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

    // Try the current endpoint first, fallback to regular announcements
    try {
      const response = await axiosInstance.get(`/communication/announcements/current/${queryString}`);
      console.log('✅ Announcements fetched successfully:', response.data);
      return response.data.results || response.data;
    } catch (currentError) {
      console.log('Current endpoint failed, trying regular announcements...');
      const response = await axiosInstance.get(`/communication/announcements/${queryString}`);
      console.log('✅ Announcements fetched from regular endpoint:', response.data);
      return (response.data.results || response.data).slice(0, 5);
    }
  } catch (error: any) {
    console.warn('❌ Announcements API failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    return [];
  }
};

/**
 * Fetches real upcoming events from the communication API
 */
const fetchUpcomingEvents = async (params?: DashboardParams) => {
  try {
    console.log('📅 Fetching upcoming events...');
    const queryParams = new URLSearchParams();
    if (params?.schoolId) queryParams.append('school', params.schoolId.toString());
    if (params?.branchId) queryParams.append('branch', params.branchId.toString());
    queryParams.append('limit', '5'); // Get next 5 events

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

    // Try the upcoming endpoint first, fallback to regular events
    try {
      const response = await axiosInstance.get(`/communication/events/upcoming/${queryString}`);
      console.log('✅ Events fetched successfully:', response.data);
      return response.data.results || response.data;
    } catch (upcomingError) {
      console.log('Upcoming endpoint failed, trying regular events...');
      const response = await axiosInstance.get(`/communication/events/${queryString}`);
      console.log('✅ Events fetched from regular endpoint:', response.data);
      return (response.data.results || response.data).slice(0, 5);
    }
  } catch (error: any) {
    console.warn('❌ Events API failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    return [];
  }
};

/**
 * Fetches school statistics from the statistics API
 */
const fetchSchoolStatistics = async (params?: DashboardParams) => {
  try {
    const queryParams = new URLSearchParams();
    if (params?.schoolId) queryParams.append('school_id', params.schoolId.toString());

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

    const response = await axiosInstance.get(`/statistics/statistics/school/${queryString}`);
    return response.data;
  } catch (error) {
    console.warn('School statistics API not available:', error);
    return null;
  }
};

/**
 * Fetches all dashboard data for the current user
 * @param params Optional parameters for filtering dashboard data
 * @returns Dashboard data or null if there was an error
 */
export const getAllDashboardData = async (params?: DashboardParams): Promise<DashboardData | null> => {
  try {
    console.log('🚀 Fetching dashboard data with params:', params);

    // Check if user is authenticated
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('⚠️ No authentication token found');
      return getMockDashboardData();
    }

    console.log('✅ Authentication token found, proceeding with API calls...');

    // Fetch data from multiple sources in parallel
    // Temporarily disable problematic endpoints and focus on working ones
    const [userCounts, academicMetrics, announcements, events] = await Promise.allSettled([
      userService.getUserCounts(params?.schoolId, params?.branchId),
      fetchAcademicMetrics(params),
      fetchRecentAnnouncements(params),
      fetchUpcomingEvents(params)
    ]);

    // Extract successful results
    const userCountsData = userCounts.status === 'fulfilled' ? userCounts.value : {
      totalStudents: 0,
      totalTeachers: 0,
      totalParents: 0,
      totalStaff: 0,
      totalAdmins: 0
    };

    // Disable problematic endpoints temporarily
    const coreData = null;
    const schoolStatsData = null;
    const academicData = academicMetrics.status === 'fulfilled' ? academicMetrics.value : null;
    const announcementsData = announcements.status === 'fulfilled' ? announcements.value : [];
    const eventsData = events.status === 'fulfilled' ? events.value : [];

    console.log('📊 Dashboard data fetched successfully:', {
      userCounts: userCountsData,
      coreMetrics: coreData,
      academicMetrics: academicData,
      schoolStats: schoolStatsData,
      announcements: announcementsData,
      events: eventsData
    });

    // Log API call results
    console.log('📈 API Call Results:');
    console.log('- User Counts:', userCounts.status === 'fulfilled' ? '✅ Success' : `❌ Failed: ${userCounts.reason}`);
    console.log('- Core Metrics: ⏸️ Temporarily disabled');
    console.log('- Academic Metrics:', academicMetrics.status === 'fulfilled' ? '✅ Success' : `❌ Failed: ${academicMetrics.reason}`);
    console.log('- School Stats: ⏸️ Temporarily disabled');
    console.log('- Announcements:', announcements.status === 'fulfilled' ? '✅ Success' : `❌ Failed: ${announcements.reason}`);
    console.log('- Events:', events.status === 'fulfilled' ? '✅ Success' : `❌ Failed: ${events.reason}`);

    // Helper function to extract value from API response objects
    const extractValue = (data: any): number => {
      if (typeof data === 'number') return data;
      if (data && typeof data === 'object' && 'value' in data) {
        console.log(`📊 Extracting value from object:`, data, `-> ${data.value}`);
        return data.value;
      }
      return 0;
    };

    // Debug log the academic data structure
    if (academicData) {
      console.log('📊 Academic data structure:', academicData);
    }

    // Build comprehensive dashboard data combining all sources
    const dashboardData: DashboardData = {
      stats: {
        totalStudents: extractValue(academicData?.students) || userCountsData.totalStudents || 0,
        totalTeachers: extractValue(academicData?.teachers) || userCountsData.totalTeachers || 0,
        totalClasses: extractValue(academicData?.classes) || 0,
        totalCourses: extractValue(academicData?.subjects) || 0,
        totalStreams: extractValue(academicData?.streams) || 0,
        totalDepartments: extractValue(academicData?.departments) || 0,
        totalEvents: eventsData.length || 0,
        totalAnnouncements: announcementsData.length || 0
      },
      recentAnnouncements: announcementsData.slice(0, 5).map((announcement: any) => ({
        id: announcement.id,
        title: announcement.title,
        date: announcement.publish_date || announcement.created_at,
        priority: announcement.priority || 'MEDIUM'
      })),
      upcomingEvents: eventsData.slice(0, 5).map((event: any) => ({
        id: event.id,
        title: event.title,
        date: event.start_date,
        location: event.location || 'TBD'
      })),
      recentActivities: [] // We'll implement this later when we have activity tracking
    };

    return dashboardData;

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return getMockDashboardData();
  }
};

/**
 * Generates mock dashboard data for development purposes
 * @returns Mock dashboard data
 */
const getMockDashboardData = (): DashboardData => {
  return {
    stats: {
      totalStudents: 0,
      totalTeachers: 0,
      totalClasses: 0,
      totalCourses: 0,
      totalStreams: 0,
      totalDepartments: 0,
      totalEvents: 0,
      totalAnnouncements: 0
    },
    recentAnnouncements: [
      {
        id: 1,
        title: 'End of Term Examinations',
        date: '2025-06-01',
        priority: 'HIGH'
      },
      {
        id: 2,
        title: 'Parents Meeting',
        date: '2025-05-15',
        priority: 'MEDIUM'
      },
      {
        id: 3,
        title: 'Sports Day',
        date: '2025-05-20',
        priority: 'LOW'
      }
    ],
    upcomingEvents: [
      {
        id: 1,
        title: 'Science Fair',
        date: '2025-05-25',
        location: 'School Hall'
      },
      {
        id: 2,
        title: 'Career Day',
        date: '2025-06-10',
        location: 'Auditorium'
      }
    ],
    recentActivities: []
  };
};

export default {
  getAllDashboardData
};

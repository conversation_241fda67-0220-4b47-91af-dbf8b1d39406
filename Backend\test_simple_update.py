#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_simple_update():
    print("🧪 Testing Simple Student Update...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Get a student to test with
    student = Student.objects.first()
    if not student:
        print("❌ No students found")
        return
        
    print(f"\n📋 Testing simple update for: {student.user.first_name} {student.user.last_name}")
    
    # Test different field combinations to isolate the issue
    test_cases = [
        {
            'name': 'Only first name',
            'data': {'first_name': 'TestName1'}
        },
        {
            'name': 'User fields only',
            'data': {
                'first_name': 'TestName2',
                'last_name': 'TestLast2',
                'email': '<EMAIL>'
            }
        },
        {
            'name': 'Student fields only',
            'data': {
                'gender': 'F' if student.gender == 'M' else 'M',
                'admission_number': f'TEST{student.admission_number}'
            }
        },
        {
            'name': 'Date field',
            'data': {
                'date_of_birth': '2008-05-15'
            }
        },
        {
            'name': 'Phone fields',
            'data': {
                'phone': '+254712345678',
                'phone_number': '+254798765432'
            }
        },
        {
            'name': 'New fields',
            'data': {
                'address': '123 Test Street',
                'emergency_contact': '+254711223344',
                'subject_type': 'Compulsory'
            }
        }
    ]
    
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    url = f'http://localhost:8000/api/users/students/{student.user.id}/'
    
    for test_case in test_cases:
        print(f"\n🔍 Testing: {test_case['name']}")
        print(f"   Data: {test_case['data']}")
        
        try:
            response = requests.patch(url, json=test_case['data'], headers=headers, timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS: {response.status_code}")
            else:
                print(f"   ❌ FAILED: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Error text: {response.text[:200]}")
                    
        except requests.exceptions.RequestException as e:
            print(f"   ❌ REQUEST ERROR: {str(e)}")

if __name__ == "__main__":
    test_simple_update()

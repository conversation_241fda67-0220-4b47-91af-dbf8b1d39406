import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { usersApi } from '../../api/usersApi';
import { academicsApi } from '../../api/academicsApi';
import { schoolsApi } from '../../api/schoolsApi';
import { studentService, type StudentFormData } from '../../services/studentService';
import Button from '../../components/ui/button/Button';
import { Input } from '../../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import PageBreadcrumb from '../../components/common/PageBreadCrumb';
import PageMeta from '../../components/common/PageMeta';

const StudentRegistrationPage = () => {
  const navigate = useNavigate();

  // Get current date in YYYY-MM-DD format
  const today = new Date().toISOString().split('T')[0];

  // Form data state
  const [formData, setFormData] = useState<StudentFormData>({
    first_name: '',
    last_name: '',
    email: '',
    gender: 'M',
    date_of_birth: '',
    school_branch_id: '',
    class_name: '',
    stream: '',
    admission_date: today,
    phone: '',
    address: '',
    emergency_contact: '',
    additional_notes: '',
    // Additional fields for enhanced form
    phone_number: '',
    guardian_name: '',
    guardian_contact: '',
    guardian_email: '',
    guardian_relationship: '',
    guardian_occupation: '',
    blood_type: '',
    medical_conditions: '',
    nationality: '',
    religion: '',
    admission_number: ''
  });

  // Form validation state
  const [errors, setErrors] = useState<Record<string, string>>({});

  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    type: 'success' as 'success' | 'error' | 'info'
  });

  // Data for dropdowns
  const [branches, setBranches] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [streams, setStreams] = useState<any[]>([]);
  const [filteredStreams, setFilteredStreams] = useState<any[]>([]);

  // Fetch data for dropdowns
  useEffect(() => {
    const fetchData = async () => {
      try {
        const branchesData = await schoolsApi.getSchoolBranches();
        setBranches(branchesData);

        const classesData = await academicsApi.getClasses();
        setClasses(classesData);

        const streamsData = await academicsApi.getStreams();
        setStreams(streamsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        setNotification({
          open: true,
          message: 'Failed to load required data. Please try again.',
          type: 'error'
        });
      }
    };

    fetchData();
  }, []);

  // Filter streams based on selected class
  useEffect(() => {
    if (formData.class_name && streams.length > 0) {
      const filtered = streams.filter(stream => stream.class_name === formData.class_name);
      setFilteredStreams(filtered);

      // Reset stream selection if current selection is not valid for the new class
      if (formData.stream && !filtered.find(s => s.id === formData.stream)) {
        setFormData(prev => ({ ...prev, stream: '' }));
      }
    } else {
      setFilteredStreams([]);
    }
  }, [formData.class_name, streams]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field when it's changed
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field when it's changed
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Date handling is now done through the standard handleChange function

  // Validate form data
  const validateForm = () => {
    const newErrors = {};

    // Required fields
    const requiredFields = [
      { field: 'first_name', label: 'First Name' },
      { field: 'last_name', label: 'Last Name' },
      { field: 'email', label: 'Email' },
      { field: 'gender', label: 'Gender' },
      { field: 'date_of_birth', label: 'Date of Birth' },
      { field: 'school_branch_id', label: 'School Branch' },
      { field: 'class_name', label: 'Class' },
      { field: 'stream', label: 'Stream' },
      { field: 'admission_date', label: 'Admission Date' }
    ];

    requiredFields.forEach(({ field, label }) => {
      if (!formData[field]) {
        newErrors[field] = `${label} is required`;
      }
    });

    // Email validation
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation (if provided)
    if (formData.phone && !/^\d{10,15}$/.test(formData.phone.replace(/[^0-9]/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      setNotification({
        open: true,
        message: 'Please fix the errors in the form before submitting.',
        type: 'error'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Use the studentService to create the student
      const response = await studentService.createStudent(formData);

      setNotification({
        open: true,
        message: 'Student registered successfully!',
        type: 'success'
      });

      // Navigate to student list after successful registration
      setTimeout(() => {
        navigate('/AllStudents');
      }, 2000);
    } catch (error: any) {
      console.error('Error registering student:', error);

      // Extract error message from response if available
      let errorMessage = 'Failed to register student. Please try again.';
      if (error.response && error.response.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (typeof error.response.data === 'object') {
          // Format error messages from the API response
          const messages = [];
          for (const [key, value] of Object.entries(error.response.data)) {
            if (Array.isArray(value)) {
              messages.push(`${key}: ${value.join(', ')}`);
            } else {
              messages.push(`${key}: ${value}`);
            }
          }
          if (messages.length > 0) {
            errorMessage = messages.join('\n');
          }
        }
      }

      setNotification({
        open: true,
        message: errorMessage,
        type: 'error'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle notification close
  const handleNotificationClose = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  return (
    <>
      <PageMeta title="Student Registration | ShuleXcel" description="Register a new student in the system." />
      <PageBreadcrumb pageTitle="Student Registration" />

      {/* Notification */}
      {notification.open && (
        <div className={`fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg ${
          notification.type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
          notification.type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
          'bg-blue-100 text-blue-800 border border-blue-200'
        }`}>
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium">{notification.message}</p>
            <button
              type="button"
              onClick={handleNotificationClose}
              className="ml-4 text-gray-400 hover:text-gray-600"
              aria-label="Close notification"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      <div className="container mx-auto px-4 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Student Registration</h1>
            <p className="text-gray-600 dark:text-gray-400">Register a new student in the system with comprehensive information.</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Personal Information Section */}
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Personal Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    First Name <span className="text-red-500">*</span>
                  </label>
                  <Input
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleChange}
                    placeholder="Enter first name"
                    className={errors.first_name ? 'border-red-500' : ''}
                  />
                  {errors.first_name && <p className="mt-1 text-sm text-red-600">{errors.first_name}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Last Name <span className="text-red-500">*</span>
                  </label>
                  <Input
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleChange}
                    placeholder="Enter last name"
                    className={errors.last_name ? 'border-red-500' : ''}
                  />
                  {errors.last_name && <p className="mt-1 text-sm text-red-600">{errors.last_name}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Address <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter email address"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Gender <span className="text-red-500">*</span>
                  </label>
                  <Select value={formData.gender} onValueChange={(value) => handleSelectChange('gender', value)}>
                    <SelectTrigger className={errors.gender ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="M">Male</SelectItem>
                      <SelectItem value="F">Female</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.gender && <p className="mt-1 text-sm text-red-600">{errors.gender}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Date of Birth <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="date"
                    name="date_of_birth"
                    value={formData.date_of_birth || ''}
                    onChange={handleChange}
                    className={errors.date_of_birth ? 'border-red-500' : ''}
                  />
                  {errors.date_of_birth && <p className="mt-1 text-sm text-red-600">{errors.date_of_birth}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phone Number
                  </label>
                  <Input
                    name="phone"
                    value={formData.phone || ''}
                    onChange={handleChange}
                    placeholder="Enter phone number"
                    className={errors.phone ? 'border-red-500' : ''}
                  />
                  {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nationality
                  </label>
                  <Input
                    name="nationality"
                    value={formData.nationality || ''}
                    onChange={handleChange}
                    placeholder="Enter nationality"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Religion
                  </label>
                  <Input
                    name="religion"
                    value={formData.religion || ''}
                    onChange={handleChange}
                    placeholder="Enter religion"
                  />
                </div>
              </div>
            </div>

            {/* School Information Section */}
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800 shadow-sm">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                  <div className="bg-green-100 dark:bg-green-800 p-2 rounded-lg mr-3">
                    <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  School Information
                </h2>
                <div className="text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 px-2 py-1 rounded-full">
                  Step 2 of 5
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* School Branch */}
                <div className="space-y-2">
                  <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                    <svg className="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    School Branch <span className="text-red-500 ml-1">*</span>
                  </label>
                  <Select value={formData.school_branch_id?.toString() || ''} onValueChange={(value) => handleSelectChange('school_branch_id', value)}>
                    <SelectTrigger className={`transition-all duration-200 ${errors.school_branch_id ? 'border-red-500 bg-red-50 dark:bg-red-900/20' : 'border-green-200 hover:border-green-300 focus:border-green-500'} ${branches.length === 0 ? 'opacity-50' : ''}`}>
                      <SelectValue placeholder={branches.length === 0 ? "Loading branches..." : "Choose your school branch"} />
                    </SelectTrigger>
                    <SelectContent>
                      {branches.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">
                          <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                          </svg>
                          No branches available
                        </div>
                      ) : (
                        branches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id.toString()} className="flex items-center">
                            <div className="flex items-center">
                              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                              <div>
                                <div className="font-medium">{branch.name}</div>
                                {branch.address && <div className="text-xs text-gray-500">{branch.address}</div>}
                              </div>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {errors.school_branch_id && (
                    <div className="flex items-center mt-1 text-sm text-red-600">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {errors.school_branch_id}
                    </div>
                  )}
                </div>

                {/* Admission Date */}
                <div className="space-y-2">
                  <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                    <svg className="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Admission Date <span className="text-red-500 ml-1">*</span>
                  </label>
                  <div className="relative">
                    <Input
                      type="date"
                      name="admission_date"
                      value={formData.admission_date || ''}
                      onChange={handleChange}
                      max={new Date().toISOString().split('T')[0]}
                      className={`transition-all duration-200 ${errors.admission_date ? 'border-red-500 bg-red-50 dark:bg-red-900/20' : 'border-green-200 hover:border-green-300 focus:border-green-500'}`}
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  {errors.admission_date && (
                    <div className="flex items-center mt-1 text-sm text-red-600">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {errors.admission_date}
                    </div>
                  )}
                  <p className="text-xs text-gray-500 dark:text-gray-400">Date when the student was admitted to the school</p>
                </div>

                {/* Admission Number */}
                <div className="space-y-2">
                  <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                    <svg className="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                    </svg>
                    Admission Number
                  </label>
                  <Input
                    name="admission_number"
                    value={formData.admission_number || ''}
                    onChange={handleChange}
                    placeholder="e.g., ADM/2025/001"
                    className="transition-all duration-200 border-green-200 hover:border-green-300 focus:border-green-500"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400">Unique identifier for the student (optional)</p>
                </div>

                {/* Class */}
                <div className="space-y-2">
                  <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                    <svg className="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    Class <span className="text-red-500 ml-1">*</span>
                  </label>
                  <Select value={formData.class_name?.toString() || ''} onValueChange={(value) => handleSelectChange('class_name', value)}>
                    <SelectTrigger className={`transition-all duration-200 ${errors.class_name ? 'border-red-500 bg-red-50 dark:bg-red-900/20' : 'border-green-200 hover:border-green-300 focus:border-green-500'} ${classes.length === 0 ? 'opacity-50' : ''}`}>
                      <SelectValue placeholder={classes.length === 0 ? "Loading classes..." : "Select student's class"} />
                    </SelectTrigger>
                    <SelectContent>
                      {classes.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">
                          <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                          </svg>
                          No classes available
                        </div>
                      ) : (
                        classes.map((classItem) => (
                          <SelectItem key={classItem.id} value={classItem.id.toString()} className="flex items-center">
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-green-100 dark:bg-green-800 rounded-lg flex items-center justify-center mr-3">
                                <span className="text-sm font-semibold text-green-600 dark:text-green-400">{classItem.name}</span>
                              </div>
                              <div>
                                <div className="font-medium">Class {classItem.name}</div>
                                <div className="text-xs text-gray-500">Academic level</div>
                              </div>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {errors.class_name && (
                    <div className="flex items-center mt-1 text-sm text-red-600">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {errors.class_name}
                    </div>
                  )}
                </div>

                {/* Stream */}
                <div className="space-y-2">
                  <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                    <svg className="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    Stream <span className="text-red-500 ml-1">*</span>
                  </label>
                  <Select
                    value={formData.stream?.toString() || ''}
                    onValueChange={(value) => handleSelectChange('stream', value)}
                  >
                    <SelectTrigger className={`transition-all duration-200 ${errors.stream ? 'border-red-500 bg-red-50 dark:bg-red-900/20' : 'border-green-200 hover:border-green-300 focus:border-green-500'} ${!formData.class_name ? 'opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800' : ''}`}>
                      <SelectValue placeholder={
                        !formData.class_name ? "Select class first" :
                        filteredStreams.length === 0 ? "No streams available for this class" :
                        "Select student's stream"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {!formData.class_name ? (
                        <div className="p-4 text-center text-gray-500">
                          <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Please select a class first
                        </div>
                      ) : filteredStreams.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">
                          <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                          No streams available for this class
                        </div>
                      ) : (
                        filteredStreams.map((stream) => (
                          <SelectItem key={stream.id} value={stream.id.toString()} className="flex items-center">
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-lg flex items-center justify-center mr-3">
                                <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">{stream.name}</span>
                              </div>
                              <div>
                                <div className="font-medium">Stream {stream.name}</div>
                                <div className="text-xs text-gray-500">Class specialization</div>
                              </div>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {errors.stream && (
                    <div className="flex items-center mt-1 text-sm text-red-600">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {errors.stream}
                    </div>
                  )}
                  {!formData.class_name && (
                    <div className="flex items-center mt-1 text-sm text-amber-600">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Stream selection will be enabled after choosing a class
                    </div>
                  )}
                </div>
              </div>

              {/* Progress Indicator */}
              <div className="mt-6 pt-4 border-t border-green-200 dark:border-green-800">
                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                  <span>School Information Progress</span>
                  <span className="font-medium">
                    {[formData.school_branch_id, formData.admission_date, formData.class_name, formData.stream].filter(Boolean).length}/4 completed
                  </span>
                </div>
                <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className={`bg-green-500 h-2 rounded-full transition-all duration-300 ${
                    [formData.school_branch_id, formData.admission_date, formData.class_name, formData.stream].filter(Boolean).length === 0 ? 'w-0' :
                    [formData.school_branch_id, formData.admission_date, formData.class_name, formData.stream].filter(Boolean).length === 1 ? 'w-1/4' :
                    [formData.school_branch_id, formData.admission_date, formData.class_name, formData.stream].filter(Boolean).length === 2 ? 'w-2/4' :
                    [formData.school_branch_id, formData.admission_date, formData.class_name, formData.stream].filter(Boolean).length === 3 ? 'w-3/4' :
                    'w-full'
                  }`}></div>
                </div>
              </div>
            </div>

            {/* Guardian Information Section */}
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Guardian Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Guardian Name
                  </label>
                  <Input
                    name="guardian_name"
                    value={formData.guardian_name || ''}
                    onChange={handleChange}
                    placeholder="Enter guardian's full name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Guardian Contact
                  </label>
                  <Input
                    name="guardian_contact"
                    value={formData.guardian_contact || ''}
                    onChange={handleChange}
                    placeholder="Enter guardian's phone number"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Guardian Email
                  </label>
                  <Input
                    type="email"
                    name="guardian_email"
                    value={formData.guardian_email || ''}
                    onChange={handleChange}
                    placeholder="Enter guardian's email"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Relationship
                  </label>
                  <Input
                    name="guardian_relationship"
                    value={formData.guardian_relationship || ''}
                    onChange={handleChange}
                    placeholder="e.g., Father, Mother, Guardian"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Guardian Occupation
                  </label>
                  <Input
                    name="guardian_occupation"
                    value={formData.guardian_occupation || ''}
                    onChange={handleChange}
                    placeholder="Enter guardian's occupation"
                  />
                </div>
              </div>
            </div>

            {/* Medical Information Section */}
            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                Medical Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Blood Type
                  </label>
                  <Input
                    name="blood_type"
                    value={formData.blood_type || ''}
                    onChange={handleChange}
                    placeholder="e.g., A+, B-, O+"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Emergency Contact
                  </label>
                  <Input
                    name="emergency_contact"
                    value={formData.emergency_contact || ''}
                    onChange={handleChange}
                    placeholder="Emergency contact number"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Medical Conditions
                  </label>
                  <textarea
                    name="medical_conditions"
                    value={formData.medical_conditions || ''}
                    onChange={handleChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    placeholder="List any medical conditions, allergies, or special needs"
                  />
                </div>
              </div>
            </div>

            {/* Additional Information Section */}
            <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Additional Information
              </h2>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Address
                  </label>
                  <textarea
                    name="address"
                    value={formData.address || ''}
                    onChange={handleChange}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    placeholder="Enter student's home address"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Additional Notes
                  </label>
                  <textarea
                    name="additional_notes"
                    value={formData.additional_notes || ''}
                    onChange={handleChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    placeholder="Any additional information about the student"
                  />
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex flex-col sm:flex-row gap-4 sm:justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                variant="outline"
                onClick={() => navigate('/AllStudents')}
                disabled={isSubmitting}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                isLoading={isSubmitting}
                loadingText="Registering..."
                className="w-full sm:w-auto"
              >
                Register Student
              </Button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default StudentRegistrationPage;

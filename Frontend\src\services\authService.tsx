import axiosInstance from '../components/utils/AxiosInstance';
import { API_ENDPOINTS } from '../config/apiEndpoints';

interface LoginRequest {
  email: string;
  password: string;
}

interface LoginResponse {
  user: {
    id?: number;
    email?: string;
    username?: string;
    user_type?: string; // This might not come from API directly
    first_name?: string;
    last_name?: string;
  };
  access: string;
  refresh: string;
}

export const authService = {
  async logout() {
    try {
      // Clear storage first to prevent any token reuse
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user_type');
      localStorage.removeItem('user');
      delete axiosInstance.defaults.headers.common['Authorization'];

      // We'll skip the server-side logout for now since it's causing 400 errors
      // This is a client-side only logout which is sufficient for most cases
      // const response = await axiosInstance.post(API_ENDPOINTS.auth.logout);

      return true;
    } catch (error) {
      // Ensure local cleanup even if something fails
      localStorage.clear();
      delete axiosInstance.defaults.headers.common['Authorization'];
      return true;
    }
  },

  async login({ email, password }: LoginRequest): Promise<LoginResponse> {
    try {
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      const response = await axiosInstance.post<LoginResponse>(API_ENDPOINTS.auth.login, {
        email,
        password
      });

      if (!response.data || !response.data.access || !response.data.refresh) {
        throw new Error('Invalid response format: missing tokens');
      }

      // Extract user data
      const userData = response.data.user;
      if (!userData) {
        throw new Error('No user data in response');
      }

      // TODO: Make a separate call to get user type or handle default type
      // For now, set a default type based on some logic (you may need to adjust this)
      const defaultUserType = userData.id === 1 ? 'admin' : 'teacher';
      const enhancedUserData = {
        ...userData,
        user_type: defaultUserType
      };

      // Store auth data using consistent keys with AxiosInstanceFixed
      localStorage.setItem('token', response.data.access);
      localStorage.setItem('refreshToken', response.data.refresh);
      localStorage.setItem('user_type', defaultUserType);
      localStorage.setItem('user', JSON.stringify(enhancedUserData));

      // Set auth header
      axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${response.data.access}`;

      return {
        user: enhancedUserData,
        access: response.data.access,
        refresh: response.data.refresh
      };
    } catch (error: any) {
      console.error('Login error details:', {
        originalError: error,
        responseData: error.response?.data,
        status: error.response?.status
      });
      throw error;
    }
  },

  // Add new method to verify token
  async verifyToken(token: string): Promise<boolean> {
    try {
      await axiosInstance.post(API_ENDPOINTS.auth.verifyToken, { token });
      return true;
    } catch {
      return false;
    }
  }
};
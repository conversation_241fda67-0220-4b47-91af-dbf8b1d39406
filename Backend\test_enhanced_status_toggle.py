#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_enhanced_status_toggle():
    print("🧪 Testing Enhanced Status Toggle Functionality...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Get a student to test with
    student = Student.objects.first()
    if not student:
        print("❌ No students found")
        return
        
    print(f"\n📋 Testing status toggle for: {student.user.first_name} {student.user.last_name}")
    print(f"   - Current status: {'Active' if student.user.is_active else 'Inactive'}")
    
    # Test toggle functionality
    original_status = student.user.is_active
    new_status = not original_status
    action = 'activate' if new_status else 'deactivate'
    
    print(f"\n🔄 Testing {action} functionality...")
    
    # Make API request to toggle status
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    url = f'http://localhost:8000/api/users/students/{student.user.id}/toggle-status/'
    
    try:
        response = requests.patch(url, json={'is_active': new_status}, headers=headers, timeout=10)
        
        print(f"\n📡 API Response:")
        print(f"   - Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ Status toggle successful!")
            
            # Refresh from database
            student.user.refresh_from_db()
            
            print(f"\n🔍 Verification:")
            print(f"   - Original status: {'Active' if original_status else 'Inactive'}")
            print(f"   - New status: {'Active' if student.user.is_active else 'Inactive'}")
            print(f"   - Expected: {'Active' if new_status else 'Inactive'}")
            
            if student.user.is_active == new_status:
                print(f"✅ Status successfully {action}d!")
                
                # Test toggle back
                print(f"\n🔄 Testing toggle back to original status...")
                response2 = requests.patch(url, json={'is_active': original_status}, headers=headers, timeout=10)
                
                if response2.status_code == 200:
                    student.user.refresh_from_db()
                    if student.user.is_active == original_status:
                        print(f"✅ Successfully toggled back to original status!")
                        print(f"🎯 Enhanced status toggle functionality is working perfectly!")
                    else:
                        print(f"❌ Failed to toggle back to original status")
                else:
                    print(f"❌ Failed to toggle back: {response2.status_code}")
            else:
                print(f"❌ Status was not updated correctly")
                
        else:
            print(f"❌ Status toggle failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Error text: {response.text[:200]}")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")

if __name__ == "__main__":
    test_enhanced_status_toggle()

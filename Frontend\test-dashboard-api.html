<!DOCTYPE html>
<html>
<head>
    <title>Test Dashboard API</title>
</head>
<body>
    <h1>Dashboard API Test</h1>
    <div id="results"></div>
    
    <script>
        async function testDashboardAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Test different school/branch combinations
                const testCases = [
                    { name: 'Sample School - Main Campus', schoolId: 3, branchId: 5 },
                    { name: 'MAKINI SCHOOL - Main', schoolId: 1, branchId: 1 },
                    { name: 'ShuleXcel Academy - Main', schoolId: 2, branchId: 2 }
                ];
                
                for (const testCase of testCases) {
                    resultsDiv.innerHTML += `<h3>Testing: ${testCase.name}</h3>`;
                    
                    const params = new URLSearchParams();
                    if (testCase.schoolId) params.append('school', testCase.schoolId.toString());
                    if (testCase.branchId) params.append('branch', testCase.branchId.toString());
                    
                    const response = await fetch(`http://localhost:8000/api/core/users/counts/?${params.toString()}`);
                    
                    if (response.ok) {
                        const data = await response.json();
                        resultsDiv.innerHTML += `
                            <p>✅ Success: Students: ${data.totalStudents}, Teachers: ${data.totalTeachers}, Total: ${data.total}</p>
                        `;
                    } else {
                        resultsDiv.innerHTML += `<p>❌ Error: ${response.status} - ${response.statusText}</p>`;
                    }
                }
            } catch (error) {
                resultsDiv.innerHTML += `<p>❌ Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        testDashboardAPI();
    </script>
</body>
</html>

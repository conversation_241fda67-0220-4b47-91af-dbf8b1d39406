from rest_framework import serializers
from .models import Teacher, Student, Staff, Parent, AdminProfile, ParentStaffLink
from django.contrib.auth import get_user_model
from core.models import CustomUser
from schools.models import SchoolBranch
from schools.serializers import SchoolBranchSerializer
import string
import random
import logging

logger = logging.getLogger(__name__)

CustomUser = get_user_model()

class AdminProfileSerializer(serializers.ModelSerializer):
    """Serializer for AdminProfile model."""
    user = serializers.PrimaryKeyRelatedField(queryset=CustomUser.objects.all(), required=False)
    school_branch = SchoolBranchSerializer(read_only=True)
    school_branch_id = serializers.PrimaryKeyRelatedField(
        queryset=SchoolBranch.objects.all(), source='school_branch', write_only=True, required=False
    )

    class Meta:
        model = AdminProfile
        fields = [
            'user', 'admin_number', 'department', 'position',
            'date_hired', 'bio', 'phone_number', 'address', 'date_of_birth',
            'gender', 'is_super_admin', 'school_branch', 'school_branch_id'
        ]
        read_only_fields = ('user',)
        swagger_schema_fields = {
            "title": "Admin Profile",
            "description": "Admin profile model with related user profile"
        }

class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for the CustomUser model."""
    profile = serializers.SerializerMethodField()
    roleName = serializers.SerializerMethodField()
    phoneNumber = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    bio = serializers.SerializerMethodField()
    customFields = serializers.SerializerMethodField()

    class Meta:
        model = CustomUser
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'user_type', 'is_active', 'date_joined', 'is_first_login', 'profile',
            'roleName', 'phoneNumber', 'address', 'bio', 'customFields'
        ]
        read_only_fields = ['id', 'username', 'email', 'date_joined']

    def get_roleName(self, obj):
        """Get the display name of the user's role."""
        try:
            return obj.get_user_type_display()
        except:
            return obj.user_type if hasattr(obj, 'user_type') else 'Unknown'

    def get_phoneNumber(self, obj):
        """Get the phone number from the user's profile."""
        try:
            if hasattr(obj, 'profile') and obj.profile:
                return getattr(obj.profile, 'phone_number', None)
            return None
        except:
            return None

    def get_address(self, obj):
        """Get the address from the user's profile."""
        try:
            if hasattr(obj, 'profile') and obj.profile:
                return getattr(obj.profile, 'address', None)
            return None
        except:
            return None

    def get_bio(self, obj):
        """Get the bio from the user's profile."""
        try:
            if hasattr(obj, 'profile') and obj.profile:
                return getattr(obj.profile, 'bio', None)
            return None
        except:
            return None

    def get_customFields(self, obj):
        """Get any custom fields from the user's profile."""
        try:
            if hasattr(obj, 'profile') and obj.profile:
                excluded_fields = ['phone_number', 'address', 'bio']
                return {
                    key: value for key, value in obj.profile.__dict__.items()
                    if not key.startswith('_') and key not in excluded_fields
                }
            return {}
        except:
            return {}

    def get_profile(self, obj):
        """Fetches the correct role-based profile."""
        try:
            # Use the profile property from the CustomUser model
            profile = obj.profile
            if not profile:
                return None

            # Map user types to their corresponding serializers
            serializer_map = {
                # Academic Staff
                'teacher': TeacherSerializer,
                'librarian': StaffSerializer,
                'counselor': StaffSerializer,

                # School Administration
                'system_admin': AdminProfileSerializer,
                'school_admin': AdminProfileSerializer,
                'deputy_principal': AdminProfileSerializer,
                'branch_admin': AdminProfileSerializer,
                'department_head': AdminProfileSerializer,
                'ict_admin': AdminProfileSerializer,

                # Support Staff
                'accountant': StaffSerializer,
                'secretary': StaffSerializer,
                'nurse': StaffSerializer,
                'maintenance': StaffSerializer,
                'security': StaffSerializer,
                'driver': StaffSerializer,

                # Students and Parents
                'student': StudentSerializer,
                'parent': ParentSerializer,

                # Legacy support
                'staff': StaffSerializer,
                'admin': AdminProfileSerializer,
            }

            # Get the appropriate serializer for the user type
            serializer_class = serializer_map.get(obj.user_type)
            if serializer_class:
                return serializer_class(profile).data

            # If no specific serializer found, return basic profile info
            return {
                'id': profile.pk,
                'user_type': obj.user_type,
                'phone_number': getattr(profile, 'phone_number', None),
                'address': getattr(profile, 'address', None),
                'bio': getattr(profile, 'bio', None),
            }

        except Exception as e:
            logger.error(f"Error getting profile for user {obj.email}: {str(e)}", exc_info=True)
            return None

class TeacherSerializer(serializers.ModelSerializer):
    """Serializer for Teacher model."""
    user = UserProfileSerializer(read_only=True)
    school_branch = SchoolBranchSerializer(read_only=True)
    school_branch_id = serializers.PrimaryKeyRelatedField(
        queryset=SchoolBranch.objects.all(), source='school_branch', write_only=True
    )

    class Meta:
        model = Teacher
        fields = [
            'user', 'is_class_teacher', 'assigned_classes', 'subjects_taught',
            'teacher_number', 'date_of_birth', 'national_id', 'phone_number',
            'email', 'personal_email', 'gender', 'profile_picture',
            'school_branch', 'school_branch_id', 'department_assignments',
            'created_at', 'updated_at'
        ]
        read_only_fields = ()
        swagger_schema_fields = {
            "title": "Teacher",
            "description": "Teacher model with related user profile"
        }

    def validate(self, data):
        """Ensure user assigned is a Teacher."""
        user = self.context['request'].user
        if user.user_type != 'teacher':
            raise serializers.ValidationError("User must be a teacher.")
        return data

    def validate_email(self, data):
        if Teacher.objects.filter(email=data).exists():
            raise serializers.ValidationError("This email is already registered.")
        return data


class StudentSerializer(serializers.ModelSerializer):
    user = UserProfileSerializer(read_only=True)
    school_branch = SchoolBranchSerializer(read_only=True)
    school_branch_id = serializers.PrimaryKeyRelatedField(
        queryset=SchoolBranch.objects.all(), source='school_branch', write_only=True
    )

    class Meta:
        model = Student
        fields = [
            'user', 'date_of_birth', 'gender', 'birth_certificate',
            'result_slip', 'admission_letter', 'school_branch', 'school_branch_id',
            'admission_date', 'class_teacher', 'class_name', 'stream',
            'admission_number', 'current_class', 'subjects',
            # Fields merged from StudentInformation
            'subject_type', 'address', 'phone_number', 'parent',
            'emergency_contact', 'additional_notes'
        ]
        read_only_fields = ()

    def validate(self, data):
        """Validate student data."""
        # If this is a create operation (no instance), we don't need to validate user type
        if self.instance is None:
            return data

        # For updates, ensure the user has appropriate permissions
        user = self.context['request'].user

        # Allow superusers and system_admins full access
        if user.is_superuser or user.user_type == 'system_admin':
            return data

        # Only allow school staff to update student records (not students themselves)
        allowed_user_types = ['admin', 'teacher', 'school_admin', 'deputy_principal', 'branch_admin']
        if user.user_type not in allowed_user_types:
            raise serializers.ValidationError(f"Only school staff can update student records. Students must visit the school admin, class teacher, or secretary to make changes.")
        return data

    def create(self, validated_data):
        """Create a new student with a user account."""
        # Extract user-related data
        email = self.initial_data.get('email')
        first_name = validated_data.get('first_name')
        last_name = validated_data.get('last_name')
        phone = self.initial_data.get('phone', '')

        if not email:
            raise serializers.ValidationError({"email": "Email is required"})

        # Check if user with this email already exists
        if CustomUser.objects.filter(email=email).exists():
            raise serializers.ValidationError({"email": "A user with this email already exists"})

        # Create the user
        user = CustomUser.objects.create_user(
            username=email,  # Use email as username
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone=phone,
            user_type='student',
            school=validated_data['school_branch'].school,
            school_branch=validated_data['school_branch']
        )

        # Set a default password (can be changed later)
        default_password = f"{first_name.lower()}{last_name.lower()[:3]}123"
        user.set_password(default_password)
        user.save()

        # Create the student profile
        student = Student.objects.create(
            user=user,
            first_name=first_name,
            last_name=last_name,
            **validated_data
        )

        return student


class StaffSerializer(serializers.ModelSerializer):
    user = UserProfileSerializer(read_only=True)
    school_branch = SchoolBranchSerializer(read_only=True)
    school_branch_id = serializers.PrimaryKeyRelatedField(
        queryset=SchoolBranch.objects.all(), source='school_branch', write_only=True
    )

    class Meta:
        model = Staff
        fields = [
            'user', 'staff_number', 'department',
            'role', 'school_branch', 'school_branch_id'
        ]
        read_only_fields = ()

    def validate(self, data):
        """Ensure user assigned is a Staff member."""
        user = self.context['request'].user
        if user.user_type != 'staff':
            raise serializers.ValidationError("User must be a staff member.")
        return data


class ParentSerializer(serializers.ModelSerializer):
    user = UserProfileSerializer(read_only=True)
    school_branch = SchoolBranchSerializer(many=True, read_only=True)
    school_branch_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=SchoolBranch.objects.all(),
        source='school_branch',
        write_only=True
    )

    class Meta:
        model = Parent
        fields = [
            'user', 'first_name', 'last_name', 'phone_number', 'email',
            'address', 'gender', 'profile_picture', 'relationship', 'children',
            'school_branch', 'school_branch_ids'
        ]
        read_only_fields = ()

    def validate(self, data):
        """Ensure user assigned is a Parent."""
        user = self.context['request'].user
        if user.user_type != 'parent':
            raise serializers.ValidationError("User must be a parent.")
        return data


class ParentStaffLinkSerializer(serializers.ModelSerializer):
    """Serializer for the ParentStaffLink model."""
    staff_user = UserProfileSerializer(read_only=True)
    staff_user_id = serializers.PrimaryKeyRelatedField(
        queryset=CustomUser.objects.filter(user_type__in=[
            'teacher', 'school_admin', 'deputy_principal', 'branch_admin',
            'department_head', 'ict_admin', 'librarian', 'counselor',
            'accountant', 'secretary', 'nurse', 'maintenance', 'security',
            'driver', 'staff'
        ]),
        source='staff_user',
        write_only=True
    )
    student = StudentSerializer(read_only=True)
    student_id = serializers.PrimaryKeyRelatedField(
        queryset=Student.objects.all(),
        source='student',
        write_only=True
    )

    class Meta:
        model = ParentStaffLink
        fields = [
            'id', 'staff_user', 'staff_user_id', 'student', 'student_id',
            'relationship', 'is_primary_contact', 'notes'
        ]
        read_only_fields = ('id',)

    def validate(self, data):
        """Validate the ParentStaffLink data."""
        # Ensure the staff user and student belong to the same school
        staff_user = data.get('staff_user')
        student = data.get('student')

        if staff_user and student and student.school_branch:
            # Check if staff user has a school or school_branch
            if not staff_user.school:
                raise serializers.ValidationError({
                    "staff_user": "Staff user must be assigned to a school."
                })

            # Check if they belong to the same school
            if staff_user.school != student.school_branch.school:
                raise serializers.ValidationError({
                    "non_field_errors": "Staff user and student must belong to the same school."
                })

        return data


class UserCreateSerializer(serializers.Serializer):
    """Enhanced serializer for creating users with any role and their profile."""
    # Basic user fields
    email = serializers.EmailField(required=True, help_text="User's email address (required and must be unique)")
    first_name = serializers.CharField(required=True, help_text="User's first name (required)")
    last_name = serializers.CharField(required=True, help_text="User's last name (required)")
    phone = serializers.CharField(required=False, allow_blank=True, help_text="User's phone number (optional)")
    user_type = serializers.ChoiceField(
        choices=CustomUser.USER_TYPE_CHOICES,
        required=True,
        help_text="Type of user account to create (required)"
    )
    school_branch = serializers.PrimaryKeyRelatedField(
        queryset=SchoolBranch.objects.all(),
        required=True,
        help_text="School branch ID the user belongs to (required)"
    )
    # Always generate a temporary password for new users
    # This field is kept for backward compatibility but will be ignored
    generate_password = serializers.BooleanField(default=True, read_only=True, help_text="Always generates a temporary password for new users")
    update_if_exists = serializers.BooleanField(default=False, help_text="Whether to update the user if they already exist (default: false)")
    send_welcome_email = serializers.BooleanField(default=True, help_text="Whether to send a welcome email with login credentials (default: true)")

    # Profile fields - these will be used based on user_type
    # Common profile fields
    gender = serializers.ChoiceField(
        choices=[('M', 'Male'), ('F', 'Female')],
        required=False,
        help_text="User's gender (optional)"
    )
    date_of_birth = serializers.DateField(
        required=False,
        allow_null=True,
        help_text="User's date of birth in YYYY-MM-DD format (optional)"
    )
    address = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="User's address (optional)"
    )
    profile_picture = serializers.ImageField(
        required=False,
        allow_null=True,
        help_text="User's profile picture (optional, max 2MB)"
    )
    emergency_contact = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Emergency contact number (optional)"
    )
    national_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="National ID or passport number (optional)"
    )

    # Student-specific fields
    admission_number = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Student's admission number (auto-generated if not provided)"
    )
    class_name = serializers.PrimaryKeyRelatedField(
        queryset=SchoolBranch.objects.all(),
        required=False,
        allow_null=True,
        help_text="Class ID the student belongs to (optional)"
    )
    stream = serializers.PrimaryKeyRelatedField(
        queryset=SchoolBranch.objects.all(),
        required=False,
        allow_null=True,
        help_text="Stream ID the student belongs to (optional)"
    )
    birth_certificate = serializers.FileField(
        required=False,
        allow_null=True,
        help_text="Student's birth certificate (optional, max 5MB)"
    )
    result_slip = serializers.FileField(
        required=False,
        allow_null=True,
        help_text="Student's previous result slip (optional, max 5MB)"
    )
    admission_letter = serializers.FileField(
        required=False,
        allow_null=True,
        help_text="Student's admission letter (optional, max 5MB)"
    )

    # Teacher-specific fields
    teacher_number = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Teacher's staff number (auto-generated if not provided)"
    )
    department = serializers.PrimaryKeyRelatedField(
        queryset=SchoolBranch.objects.all(),
        required=False,
        allow_null=True,
        help_text="Department ID the teacher belongs to (optional)"
    )
    is_class_teacher = serializers.BooleanField(
        default=False,
        required=False,
        help_text="Whether the teacher is a class teacher (default: false)"
    )
    is_hod = serializers.BooleanField(
        default=False,
        required=False,
        help_text="Whether the teacher is a head of department (default: false)"
    )
    cv_document = serializers.FileField(
        required=False,
        allow_null=True,
        help_text="Teacher's CV document (optional, max 5MB)"
    )

    # Staff-specific fields
    staff_number = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Staff member's number (auto-generated if not provided)"
    )
    role = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Staff member's role (optional)"
    )

    # Admin-specific fields
    admin_number = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Admin's staff number (auto-generated if not provided)"
    )
    position = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Admin's position (optional)"
    )
    is_super_admin = serializers.BooleanField(
        default=False,
        required=False,
        help_text="Whether the admin is a super admin (default: false)"
    )

    # Parent-specific fields
    relationship = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Parent's relationship to the children (e.g., 'Father', 'Mother', 'Guardian')"
    )
    children = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=Student.objects.all()),
        required=False,
        help_text="List of student IDs who are children of this parent (optional)"
    )

    def validate(self, data):
        """Enhanced validation for user creation and updates."""
        email = data.get('email')
        user_type = data.get('user_type')
        update_if_exists = data.get('update_if_exists', False)

        # Validate email format
        if not email or '@' not in email:
            raise serializers.ValidationError({"email": "Please provide a valid email address."})

        # Check for existing user
        if CustomUser.objects.filter(email=email).exists():
            existing_user = CustomUser.objects.get(email=email)

            if not update_if_exists:
                error_msg = f"A user with this email already exists as a {existing_user.get_user_type_display()}."

                # Check if the user has a complete profile
                has_profile = False

                # Academic Staff
                if existing_user.user_type == 'teacher' and hasattr(existing_user, 'teacher_profile'):
                    has_profile = True
                elif existing_user.user_type in ['librarian', 'counselor'] and hasattr(existing_user, 'staff_profile'):
                    has_profile = True

                # School Administration
                elif existing_user.user_type in ['school_admin', 'deputy_principal', 'branch_admin', 'department_head', 'ict_admin', 'system_admin'] and hasattr(existing_user, 'admin_profile'):
                    has_profile = True

                # Support Staff
                elif existing_user.user_type in ['accountant', 'secretary', 'nurse', 'maintenance', 'security', 'driver'] and hasattr(existing_user, 'staff_profile'):
                    has_profile = True

                # Students and Parents
                elif existing_user.user_type == 'student' and hasattr(existing_user, 'student_profile'):
                    has_profile = True
                elif existing_user.user_type == 'parent' and hasattr(existing_user, 'parent_profile'):
                    has_profile = True

                # Legacy support
                elif existing_user.user_type == 'staff' and hasattr(existing_user, 'staff_profile'):
                    has_profile = True
                elif existing_user.user_type == 'admin' and hasattr(existing_user, 'admin_profile'):
                    has_profile = True

                if not has_profile:
                    error_msg += " However, their profile is incomplete. You can set update_if_exists=true to update this user."
                else:
                    error_msg += " If you want to update this user, set update_if_exists=true."

                raise serializers.ValidationError({"email": error_msg})
            elif existing_user.user_type != user_type:
                # If updating an existing user with a different user_type, warn about it
                raise serializers.ValidationError({
                    "user_type": f"Cannot change user type from {existing_user.get_user_type_display()} to {dict(CustomUser.USER_TYPE_CHOICES)[user_type]}. "
                                 f"Please create a new user with a different email address instead."
                })

        # Validate user type specific fields
        self._validate_user_type_fields(data)

        return data

    def _validate_user_type_fields(self, data):
        """Validate fields specific to each user type."""
        user_type = data.get('user_type')

        # Student validation
        if user_type == 'student':
            # Validate student-specific fields
            if data.get('class_name') and not data.get('stream'):
                # If class is provided but stream is not, it's just a warning, not an error
                pass

        # Teacher validation
        elif user_type == 'teacher':
            # Validate teacher-specific fields
            if data.get('is_class_teacher') and not data.get('department'):
                raise serializers.ValidationError({"department": "Department is required for class teachers."})

        # Department Head validation
        elif user_type == 'department_head':
            if not data.get('department'):
                raise serializers.ValidationError({"department": "Department is required for department heads."})

        # School Administration validation
        elif user_type in ['school_admin', 'deputy_principal', 'branch_admin', 'ict_admin']:
            if not data.get('position'):
                # Auto-generate position based on user_type if not provided
                data['position'] = user_type.replace('_', ' ').title()

        # Parent validation
        elif user_type == 'parent':
            # Validate parent-specific fields
            if not data.get('relationship') and data.get('children'):
                raise serializers.ValidationError({"relationship": "Relationship to children is required when adding children."})

        # Support Staff validation
        elif user_type in ['accountant', 'secretary', 'nurse', 'maintenance', 'security', 'driver']:
            if not data.get('role'):
                # Auto-set role based on user_type if not provided
                data['role'] = user_type

        # Validate file sizes if files are provided
        self._validate_file_sizes(data)

    def _validate_file_sizes(self, data):
        """Validate file sizes for uploaded files."""
        # Profile picture - max 2MB
        if 'profile_picture' in data and data['profile_picture']:
            if data['profile_picture'].size > 2 * 1024 * 1024:  # 2MB in bytes
                raise serializers.ValidationError({"profile_picture": "Profile picture must be less than 2MB."})

        # Document files - max 5MB each
        document_fields = ['birth_certificate', 'result_slip', 'admission_letter', 'cv_document']
        for field in document_fields:
            if field in data and data[field]:
                if data[field].size > 5 * 1024 * 1024:  # 5MB in bytes
                    raise serializers.ValidationError({field: f"{field.replace('_', ' ').title()} must be less than 5MB."})

    def generate_temporary_password(self, length=10):
        """Generate a secure temporary password."""
        characters = string.ascii_letters + string.digits + string.punctuation
        # Ensure at least one of each type of character
        password = [
            random.choice(string.ascii_lowercase),
            random.choice(string.ascii_uppercase),
            random.choice(string.digits),
            random.choice('!@#$%^&*()_+-=[]{}|;:,.<>?')
        ]
        # Fill the rest with random characters
        password.extend(random.choice(characters) for _ in range(length - 4))
        # Shuffle the password characters
        random.shuffle(password)
        return ''.join(password)

    def create(self, validated_data):
        """Enhanced method to create a new user or update an existing one with the appropriate profile."""
        # Extract basic user data
        email = validated_data.get('email')
        first_name = validated_data.get('first_name')
        last_name = validated_data.get('last_name')
        phone = validated_data.get('phone', '')
        user_type = validated_data.get('user_type')
        school_branch = validated_data.get('school_branch')
        generate_password = validated_data.get('generate_password', True)
        update_if_exists = validated_data.get('update_if_exists', False)
        send_welcome_email = validated_data.get('send_welcome_email', True)

        # Extract common profile data
        profile_data = {
            'gender': validated_data.get('gender'),
            'date_of_birth': validated_data.get('date_of_birth'),
            'address': validated_data.get('address'),
            'emergency_contact': validated_data.get('emergency_contact'),
            'national_id': validated_data.get('national_id')
        }

        # Handle profile picture if provided
        if 'profile_picture' in validated_data and validated_data['profile_picture']:
            profile_data['profile_picture'] = validated_data['profile_picture']

        # Add user type specific profile data
        if user_type == 'student':
            profile_data.update({
                'admission_number': validated_data.get('admission_number'),
                'class_name': validated_data.get('class_name'),
                'stream': validated_data.get('stream'),
                'first_name': first_name,  # Student model has its own first_name field
                'last_name': last_name,    # Student model has its own last_name field
                'school_branch': school_branch
            })

            # Handle student-specific file uploads
            for field in ['birth_certificate', 'result_slip', 'admission_letter']:
                if field in validated_data and validated_data[field]:
                    profile_data[field] = validated_data[field]

        elif user_type == 'teacher':
            profile_data.update({
                'teacher_number': validated_data.get('teacher_number'),
                'department': validated_data.get('department'),
                'is_class_teacher': validated_data.get('is_class_teacher', False),
                'is_hod': validated_data.get('is_hod', False),
                'school_branch': school_branch
            })

            # Handle teacher-specific file uploads
            if 'cv_document' in validated_data and validated_data['cv_document']:
                profile_data['cv_document'] = validated_data['cv_document']

        elif user_type == 'staff':
            profile_data.update({
                'staff_number': validated_data.get('staff_number'),
                'role': validated_data.get('role', 'staff'),
                'school_branch': school_branch,
                'department': validated_data.get('department')
            })

        elif user_type == 'admin':
            profile_data.update({
                'admin_number': validated_data.get('admin_number'),
                'position': validated_data.get('position'),
                'is_super_admin': validated_data.get('is_super_admin', False),
                'department': validated_data.get('department'),
                'school_branch': school_branch
            })

        elif user_type == 'parent':
            profile_data.update({
                'relationship': validated_data.get('relationship'),
                'first_name': first_name,  # Parent model has its own first_name field
                'last_name': last_name,    # Parent model has its own last_name field
                'phone_number': phone,      # Parent model has its own phone_number field
                'email': email              # Parent model has its own email field
            })
            if validated_data.get('children'):
                profile_data['children'] = validated_data.get('children')

        # Check if user exists
        existing_user = None
        if CustomUser.objects.filter(email=email).exists():
            existing_user = CustomUser.objects.get(email=email)

        # Always generate a temporary password for new users
        temporary_password = self.generate_temporary_password()
        password = temporary_password

        # Disable the signal temporarily to prevent automatic profile creation
        from django.db.models.signals import post_save
        from users.signals import save_user_role_profile
        post_save.disconnect(save_user_role_profile, sender=CustomUser)

        try:
            if existing_user and update_if_exists:
                # Update existing user
                existing_user.first_name = first_name
                existing_user.last_name = last_name
                existing_user.phone = phone

                # We already validated that user_type can't be changed in the validate method
                # So we don't need to check it here again

                existing_user.school_branch = school_branch
                existing_user.school = school_branch.school

                # Update password if requested
                if generate_password:
                    existing_user.set_password(password)

                existing_user.save()
                user = existing_user
                action_performed = "updated"
            else:
                # Create new user
                user = CustomUser.objects.create(
                    email=email,
                    username=email,  # Use email as username
                    first_name=first_name,
                    last_name=last_name,
                    phone=phone,
                    user_type=user_type,
                    school_branch=school_branch,
                    school=school_branch.school,  # Set the school based on the branch
                    is_first_login=True  # Set first login flag
                )

                # Set the password
                user.set_password(password)
                user.save()
                action_performed = "created"

            # Create or update the profile manually
            profile_instance = None
            if user_type == 'student':
                student, _ = Student.objects.get_or_create(user=user)
                for key, value in profile_data.items():
                    if value is not None:
                        setattr(student, key, value)
                student.save()
                profile_instance = student

            elif user_type == 'teacher':
                teacher, _ = Teacher.objects.get_or_create(user=user)
                for key, value in profile_data.items():
                    if value is not None:
                        setattr(teacher, key, value)
                teacher.save()
                profile_instance = teacher

            elif user_type == 'staff':
                staff, _ = Staff.objects.get_or_create(user=user)
                for key, value in profile_data.items():
                    if value is not None:
                        setattr(staff, key, value)
                staff.save()
                profile_instance = staff

            elif user_type == 'admin':
                admin, _ = AdminProfile.objects.get_or_create(user=user)
                for key, value in profile_data.items():
                    if value is not None:
                        setattr(admin, key, value)
                admin.save()
                profile_instance = admin

            elif user_type == 'parent':
                parent, _ = Parent.objects.get_or_create(user=user)
                for key, value in profile_data.items():
                    if key != 'children' and value is not None:
                        setattr(parent, key, value)
                parent.save()
                # Handle many-to-many relationship for children
                if 'children' in profile_data and profile_data['children']:
                    parent.children.set(profile_data['children'])
                profile_instance = parent

            # Send welcome email if requested
            if send_welcome_email and temporary_password:
                self._send_welcome_email(user, temporary_password, user_type)

        except Exception as e:
            # Log the error for debugging
            logger.error(f"Error creating/updating user: {str(e)}")

            # Re-raise as a serializer error with a user-friendly message
            raise serializers.ValidationError({
                "error": f"Failed to {action_performed} user: {str(e)}"
            })

        finally:
            # Reconnect the signal
            post_save.connect(save_user_role_profile, sender=CustomUser)

        # Return user data with temporary password if generated
        response_data = {
            'id': user.id,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'user_type': user.user_type,
            'action_performed': action_performed,
            'profile_id': getattr(profile_instance, 'id', None)
        }

        if temporary_password:
            response_data['temporary_password'] = temporary_password

        return response_data

    def _send_welcome_email(self, user, password, user_type):
        """Send welcome email with login credentials."""
        try:
            from django.core.mail import EmailMultiAlternatives
            from django.template.loader import render_to_string
            from django.utils.html import strip_tags
            from django.conf import settings

            # Get the frontend URL from settings
            frontend_url = settings.FRONTEND_URL
            if not frontend_url.endswith('/'):
                frontend_url += '/'

            login_url = f"{frontend_url}signin"

            context = {
                'user': user,
                'password': password,
                'user_type': dict(CustomUser.USER_TYPE_CHOICES).get(user_type, user_type),
                'login_url': login_url
            }

            # Render email templates
            html_content = render_to_string('emails/welcome_email.html', context)
            text_content = strip_tags(html_content)

            # Create email message
            subject = f"Welcome to ShuleXcel - Your Account Details"
            from_email = settings.DEFAULT_FROM_EMAIL
            to_email = user.email

            msg = EmailMultiAlternatives(subject, text_content, from_email, [to_email])
            msg.attach_alternative(html_content, "text/html")
            msg.send()

        except Exception as e:
            # Log the error but don't fail the user creation process
            logger.error(f"Failed to send welcome email: {str(e)}")

import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from 'react-redux';
import { saveAs } from "file-saver";

import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "../../components/ui/table";
import Input from "../../components/form/input/InputField";
import Select from "../../components/form/Select";
import Button from "../../components/ui/button/Button";

import { studentService, type Student } from "../../services/studentService";
import academicsService from "../../services/academicsService.ts";
import { RootState } from '../../store/store';
import { useSchool } from '../../contexts/SchoolContext';

export default function AllStudents() {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGrade, setSelectedGrade] = useState("All");
  type Grade = { name: string }; // Define the Grade type
  const [grades, setGrades] = useState<Grade[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalStudents, setTotalStudents] = useState(0);
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);

  // Get the selected school and branch from context (same as Dashboard)
  const { selectedSchool, selectedBranch } = useSchool();

  useEffect(() => {
    const timer = setTimeout(() => {
      fetchStudents();
    }, 500);

    return () => clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, selectedGrade, searchQuery, selectedSchool, selectedBranch]); // Re-fetch when school/branch changes

  useEffect(() => {
    // Fetch grades when component mounts
    const fetchGrades = async () => {
      try {
        // TODO: Fix academicsService.getGrades function
        // For now, use hardcoded grades to prevent errors
        const gradesData = [
          { name: 'Grade 1' },
          { name: 'Grade 2' },
          { name: 'Grade 3' },
          { name: 'Grade 4' },
          { name: 'Grade 5' },
          { name: 'Grade 6' },
          { name: 'Grade 7' },
          { name: 'Grade 8' },
          { name: 'Form 1' },
          { name: 'Form 2' },
          { name: 'Form 3' },
          { name: 'Form 4' }
        ];
        setGrades(gradesData);
        console.log('✅ Grades loaded (hardcoded):', gradesData);
      } catch (error) {
        console.error('Error fetching grades:', error);
      }
    };

    fetchGrades();
  }, []);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log(`🔍 Fetching students for School: ${selectedSchool?.name} (ID: ${selectedSchool?.id}), Branch: ${selectedBranch?.name} (ID: ${selectedBranch?.id})`);

      // If no branch is selected, show a message instead of making API call
      if (!selectedBranch?.id) {
        console.log('⚠️ No branch selected, cannot fetch students');
        setStudents([]);
        setTotalPages(0);
        setTotalStudents(0);
        setError('Please select a school and branch to view students');
        return;
      }

      const response = await studentService.getAllStudents({
        grade: selectedGrade !== "All" ? selectedGrade : undefined,
        search: searchQuery.trim() || undefined,
        page: currentPage,
        page_size: 10,
        school_branch: selectedBranch.id, // Use selected branch from context
        // Temporarily remove is_active filter for debugging
        // is_active: true
      });

      console.log(`✅ Students fetched: ${response.results?.length || 0} students found`);
      console.log('📊 API Response:', response);

      setStudents(response.results || []);
      setTotalPages(Math.ceil((response.count || 0) / 10));
      setTotalStudents(response.count || 0);
    } catch (err: unknown) {
      const error = err as { response?: { data?: { message?: string }, status?: number }, message?: string };
      console.error('❌ Error fetching students:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        fullError: error
      });
      setError(error.response?.data?.message || error.message || "Failed to fetch students");
      setStudents([]);
    } finally {
      setLoading(false);
    }
  };

  const getStartingIndex = () => {
    return (currentPage - 1) * 10 + 1;
  };

  const handleExportExcel = async () => {
    try {
      const blob = await studentService.exportToExcel();
      saveAs(blob, "students.xlsx");
    } catch (err) {
      console.error("Export failed:", err);
      setError("Failed to export to Excel");
    }
  };

  const handleExportPDF = async () => {
    try {
      const blob = await studentService.exportToPDF();
      saveAs(blob, "students.pdf");
    } catch (err) {
      console.error("Export failed:", err);
      setError("Failed to export to PDF");
    }
  };

  const handleClearSearch = () => {
    setSearchQuery("");
    setCurrentPage(1);
  };

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-600 p-4 rounded">
        <h3 className="font-medium">Error loading students</h3>
        <p>{error}</p>
        <button
          type="button"
          onClick={fetchStudents}
          className="mt-2 text-sm text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div>
      <PageMeta title="All Students | ShuleXcel" description="List of all students." />
      <PageBreadcrumb pageTitle="All Students" />

      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03] p-4">
        {/* Header & Actions */}
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="font-semibold text-gray-800 text-theme-xl dark:text-white/90 sm:text-2xl">
              Student List
            </h3>
            {selectedSchool && selectedBranch ? (
              <p className="text-sm text-gray-600 mt-1">
                📍 {selectedSchool.name} - {selectedBranch.name}
              </p>
            ) : (
              <p className="text-sm text-orange-600 mt-1">
                ⚠️ Please select a school and branch to view students
              </p>
            )}
          </div>
          <Button
            variant="primary"
            onClick={() => navigate("/students/register")}
          >
            Add Student
          </Button>
        </div>

        {/* Search & Filter */}
        <div className="flex flex-wrap gap-4 mb-4">
          <div className="relative">
            <Input
              type="text"
              placeholder="Search by name or admission number..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-64 pr-10"
            />
            {searchQuery && (
              <button
                type="button"
                onClick={handleClearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            )}
          </div>

          <Select
            options={[
              { value: "All", label: "All Grades" },
              ...grades.map(grade => ({ value: grade.name, label: grade.name }))
            ]}
            value={selectedGrade}
            onChange={(value) => {
              setSelectedGrade(value);
              setCurrentPage(1);
            }}
            className="w-48"
          />

          <div className="flex gap-2 ml-auto">
            <Button
              className="bg-orange-300 hover:bg-orange-600 text-white-800"
              variant="outline"
              onClick={() => navigate('/inactive-students')}
            >
              View Inactive
            </Button>
            <Button
              className="bg-green-300 hover:bg-green-600 text-white-800"
              variant="secondary"
              onClick={handleExportExcel}
              disabled={loading}
            >
              {loading ? "Loading..." : "Export Excel"}
            </Button>
            <Button
              className="bg-red-300 hover:bg-red-600 text-white-800"
              variant="secondary"
              onClick={handleExportPDF}
              disabled={loading}
            >
              {loading ? "Loading..." : "Export PDF"}
            </Button>
          </div>
        </div>

        {/* Student Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableCell isHeader>#</TableCell>
                <TableCell isHeader>Admission Number</TableCell>
                <TableCell isHeader>Student Name</TableCell>
                <TableCell isHeader>Gender</TableCell>
                <TableCell isHeader>Grade</TableCell>
                <TableCell isHeader>Class</TableCell>
                <TableCell isHeader>Stream</TableCell>
                <TableCell isHeader>Admission Date</TableCell>
                <TableCell isHeader>Actions</TableCell>
              </TableRow>
            </TableHeader>

            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
                    <p className="mt-2">Loading students...</p>
                  </TableCell>
                </TableRow>
              ) : students.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    <p className="text-gray-500">No students found</p>
                    {searchQuery && (
                      <button
                        type="button"
                        onClick={handleClearSearch}
                        className="mt-2 text-sm text-blue-600 hover:underline"
                      >
                        Clear search
                      </button>
                    )}
                  </TableCell>
                </TableRow>
              ) : (
                students.map((student, index) => (
                  <TableRow key={student.id} className="hover:bg-gray-100 dark:hover:bg-gray-700">
                    <TableCell>{getStartingIndex() + index}</TableCell>
                    <TableCell>{student.admission_number}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div>
                          <span className="block font-medium text-gray-800 text-theme-sm dark:text-white/90">
                            {student.first_name} {student.last_name}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{student.gender === 'M' ? 'Male' : student.gender === 'F' ? 'Female' : '-'}</TableCell>
                    <TableCell>{student.grade}</TableCell>
                    <TableCell>{student.class_name}</TableCell>
                    <TableCell>{student.stream_name || '-'}</TableCell>
                    <TableCell>{student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '-'}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <button
                          type="button"
                          onClick={() => navigate(`/student/${student.id}`)}
                          className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                          title="View Details"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>
                        <button
                          type="button"
                          onClick={() => navigate(`/edit-student/${student.id}`)}
                          className="p-1 text-green-600 hover:bg-green-50 rounded"
                          title="Edit Student"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            if (window.confirm(`Are you sure you want to deactivate ${student.first_name} ${student.last_name}?`)) {
                              studentService.toggleStudentStatus(student.id, false)
                                .then(() => fetchStudents())
                                .catch(err => {
                                  const error = err as { response?: { data?: { message?: string } }, message?: string };
                                  alert(`Failed to deactivate student: ${error.response?.data?.message || error.message || 'Unknown error'}`);
                                });
                            }
                          }}
                          className="p-1 text-red-600 hover:bg-red-50 rounded"
                          title="Deactivate Student"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                          </svg>
                        </button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {students.length > 0 && (
          <div className="flex justify-between items-center mt-4">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Showing {students.length} of {totalStudents} students (Page {currentPage} of {totalPages})
            </span>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1 || loading}
                className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50 hover:bg-gray-300"
              >
                Previous
              </button>
              <button
                type="button"
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages || loading}
                className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50 hover:bg-gray-300"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
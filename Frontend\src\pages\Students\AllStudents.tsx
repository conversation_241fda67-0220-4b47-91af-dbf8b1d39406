import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from 'react-redux';
import { saveAs } from "file-saver";

import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "../../components/ui/table";
import Input from "../../components/form/input/InputField";
import Select from "../../components/form/Select";
import Button from "../../components/ui/button/Button";

import { studentService, type Student } from "../../services/studentService";
import academicsService from "../../services/academicsService.ts";
import { RootState } from '../../store/store';
import { useSchool } from '../../contexts/SchoolContext';

export default function AllStudents() {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGrade, setSelectedGrade] = useState("All");
  type Grade = { name: string }; // Define the Grade type
  const [grades, setGrades] = useState<Grade[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalStudents, setTotalStudents] = useState(0);
  const [selectedStudents, setSelectedStudents] = useState<number[]>([]);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [sortBy, setSortBy] = useState<'name' | 'admission_number' | 'grade' | 'admission_date'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('active');
  const [statistics, setStatistics] = useState({
    totalActive: 0,
    totalInactive: 0,
    byGrade: {} as Record<string, number>,
    byGender: { male: 0, female: 0 }
  });
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);

  // Get the selected school and branch from context (same as Dashboard)
  const { selectedSchool, selectedBranch } = useSchool();

  useEffect(() => {
    const timer = setTimeout(() => {
      fetchStudents();
    }, 500);

    return () => clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, selectedGrade, searchQuery, selectedSchool, selectedBranch]); // Re-fetch when school/branch changes

  useEffect(() => {
    // Fetch grades when component mounts
    const fetchGrades = async () => {
      try {
        // TODO: Fix academicsService.getGrades function
        // For now, use hardcoded grades to prevent errors
        const gradesData = [
          { name: 'Grade 1' },
          { name: 'Grade 2' },
          { name: 'Grade 3' },
          { name: 'Grade 4' },
          { name: 'Grade 5' },
          { name: 'Grade 6' },
          { name: 'Grade 7' },
          { name: 'Grade 8' },
          { name: 'Form 1' },
          { name: 'Form 2' },
          { name: 'Form 3' },
          { name: 'Form 4' }
        ];
        setGrades(gradesData);
        console.log('✅ Grades loaded (hardcoded):', gradesData);
      } catch (error) {
        console.error('Error fetching grades:', error);
      }
    };

    fetchGrades();
  }, []);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log(`🔍 Fetching students for School: ${selectedSchool?.name} (ID: ${selectedSchool?.id}), Branch: ${selectedBranch?.name} (ID: ${selectedBranch?.id})`);

      // Build parameters object
      const params: any = {
        grade: selectedGrade !== "All" ? selectedGrade : undefined,
        search: searchQuery.trim() || undefined,
        page: currentPage,
        page_size: 10,
        // Temporarily remove is_active filter for debugging
        // is_active: true
      };

      // Only add branch filter if a branch is selected
      if (selectedBranch?.id) {
        params.school_branch = selectedBranch.id;
        console.log(`🔍 Filtering by branch: ${selectedBranch.name} (ID: ${selectedBranch.id})`);
      } else {
        console.log('🔍 No branch selected - showing all students');
      }

      const response = await studentService.getAllStudents(params);

      console.log(`✅ Students fetched: ${response.results?.length || 0} students found`);
      console.log('📊 API Response:', response);
      console.log(`📊 Total count from API: ${response.count}`);

      // Debug: Check student data structure
      if (response.results && response.results.length > 0) {
        console.log('📊 First student data:', response.results[0]);
        console.log('📊 Student IDs:', response.results.map(s => s.id));
        console.log('📊 Students by branch:', response.results.reduce((acc: any, s: any) => {
          const branch = s.school_branch?.name || 'No Branch';
          acc[branch] = (acc[branch] || 0) + 1;
          return acc;
        }, {}));
      } else {
        console.log('⚠️ No students returned from API');
        if (selectedBranch?.id) {
          console.log(`⚠️ This might be because branch "${selectedBranch.name}" has no students`);
        }
      }

      const studentsData = response.results || [];
      setStudents(studentsData);
      setTotalPages(Math.ceil((response.count || 0) / 10));
      setTotalStudents(response.count || 0);

      // Calculate statistics
      const stats = {
        totalActive: studentsData.filter(s => s.user?.is_active !== false).length,
        totalInactive: studentsData.filter(s => s.user?.is_active === false).length,
        byGrade: {} as Record<string, number>,
        byGender: {
          male: studentsData.filter(s => s.gender === 'M').length,
          female: studentsData.filter(s => s.gender === 'F').length
        }
      };

      // Count by grade
      studentsData.forEach(student => {
        const grade = student.grade || 'Unknown';
        stats.byGrade[grade] = (stats.byGrade[grade] || 0) + 1;
      });

      setStatistics(stats);
    } catch (err: unknown) {
      const error = err as { response?: { data?: { message?: string }, status?: number }, message?: string };
      console.error('❌ Error fetching students:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        fullError: error
      });
      setError(error.response?.data?.message || error.message || "Failed to fetch students");
      setStudents([]);
    } finally {
      setLoading(false);
    }
  };

  const getStartingIndex = () => {
    return (currentPage - 1) * 10 + 1;
  };

  const handleExportExcel = async () => {
    try {
      const blob = await studentService.exportToExcel();
      saveAs(blob, "students.xlsx");
    } catch (err) {
      console.error("Export failed:", err);
      setError("Failed to export to Excel");
    }
  };

  const handleExportPDF = async () => {
    try {
      const blob = await studentService.exportToPDF();
      saveAs(blob, "students.pdf");
    } catch (err) {
      console.error("Export failed:", err);
      setError("Failed to export to PDF");
    }
  };

  const handleClearSearch = () => {
    setSearchQuery("");
    setCurrentPage(1);
  };

  // Bulk actions
  const handleSelectAll = () => {
    if (selectedStudents.length === students.length) {
      setSelectedStudents([]);
    } else {
      setSelectedStudents(students.map(s => s.id).filter(id => id !== undefined) as number[]);
    }
  };

  const handleSelectStudent = (studentId: number) => {
    setSelectedStudents(prev =>
      prev.includes(studentId)
        ? prev.filter(id => id !== studentId)
        : [...prev, studentId]
    );
  };

  const handleBulkAction = async (action: 'activate' | 'deactivate' | 'export') => {
    if (selectedStudents.length === 0) {
      alert('Please select students first');
      return;
    }

    switch (action) {
      case 'activate':
        if (window.confirm(`🔄 Bulk Activate Students\n\nActivate ${selectedStudents.length} selected students?\n\nThis will:\n• Restore access to the system\n• Make students visible in active lists\n• Allow login and participation in activities`)) {
          try {
            setLoading(true);
            let successCount = 0;
            let errorCount = 0;

            for (const studentId of selectedStudents) {
              try {
                await studentService.toggleStudentStatus(studentId, true);
                successCount++;
              } catch (error) {
                console.error(`Failed to activate student ${studentId}:`, error);
                errorCount++;
              }
            }

            setSelectedStudents([]);
            fetchStudents(); // Refresh the list

            if (errorCount === 0) {
              alert(`✅ Success!\n\nActivated ${successCount} students successfully.`);
            } else {
              alert(`⚠️ Partial Success\n\nActivated: ${successCount} students\nFailed: ${errorCount} students\n\nPlease check the failed students individually.`);
            }
          } catch (error) {
            console.error('Bulk activate error:', error);
            alert('❌ Failed to activate students. Please try again.');
          } finally {
            setLoading(false);
          }
        }
        break;
      case 'deactivate':
        if (window.confirm(`⚠️ Bulk Deactivate Students\n\nDeactivate ${selectedStudents.length} selected students?\n\nThis will:\n• Remove access to the system\n• Hide from active student lists\n• Prevent login and participation\n\nThis action can be reversed later.`)) {
          try {
            setLoading(true);
            let successCount = 0;
            let errorCount = 0;

            for (const studentId of selectedStudents) {
              try {
                await studentService.toggleStudentStatus(studentId, false);
                successCount++;
              } catch (error) {
                console.error(`Failed to deactivate student ${studentId}:`, error);
                errorCount++;
              }
            }

            setSelectedStudents([]);
            fetchStudents(); // Refresh the list

            if (errorCount === 0) {
              alert(`✅ Success!\n\nDeactivated ${successCount} students successfully.`);
            } else {
              alert(`⚠️ Partial Success\n\nDeactivated: ${successCount} students\nFailed: ${errorCount} students\n\nPlease check the failed students individually.`);
            }
          } catch (error) {
            console.error('Bulk deactivate error:', error);
            alert('❌ Failed to deactivate students. Please try again.');
          } finally {
            setLoading(false);
          }
        }
        break;
      case 'export':
        handleExportSelected();
        break;
    }
  };

  const handleExportSelected = () => {
    const selectedStudentData = students.filter(s => selectedStudents.includes(s.id));
    const csvContent = generateCSV(selectedStudentData);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, `selected_students_${new Date().toISOString().split('T')[0]}.csv`);
  };

  const generateCSV = (data: Student[]) => {
    const headers = ['Name', 'Admission Number', 'Gender', 'Grade', 'Class', 'Stream', 'Status'];
    const rows = data.map(student => [
      `${student.first_name} ${student.last_name}`,
      student.admission_number || '',
      student.gender === 'M' ? 'Male' : 'Female',
      student.grade || '',
      student.class_name || '',
      student.stream_name || '',
      student.user?.is_active ? 'Active' : 'Inactive'
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-600 p-4 rounded">
        <h3 className="font-medium">Error loading students</h3>
        <p>{error}</p>
        <button
          type="button"
          onClick={fetchStudents}
          className="mt-2 text-sm text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div>
      <PageMeta title="All Students | ShuleXcel" description="List of all students." />
      <PageBreadcrumb pageTitle="All Students" />

      <div className="space-y-6">
        {/* Enhanced Header with Statistics */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-3xl font-bold mb-2">Student Management</h1>
              {selectedSchool && selectedBranch ? (
                <p className="text-blue-100 flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  {selectedSchool.name} - {selectedBranch.name}
                </p>
              ) : (
                <p className="text-orange-200 flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  Please select a school and branch to view students
                </p>
              )}
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => navigate("/students/register")}
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add Student
              </Button>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Total Students</p>
                  <p className="text-2xl font-bold">{totalStudents}</p>
                </div>
                <div className="bg-white/20 rounded-full p-2">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Active</p>
                  <p className="text-2xl font-bold text-green-300">{statistics.totalActive}</p>
                </div>
                <div className="bg-green-500/20 rounded-full p-2">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Male</p>
                  <p className="text-2xl font-bold text-blue-300">{statistics.byGender.male}</p>
                </div>
                <div className="bg-blue-500/20 rounded-full p-2">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Female</p>
                  <p className="text-2xl font-bold text-pink-300">{statistics.byGender.female}</p>
                </div>
                <div className="bg-pink-500/20 rounded-full p-2">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Filters and Controls */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">

        {/* Search & Filter */}
        <div className="flex flex-wrap gap-4 mb-4">
          <div className="relative">
            <Input
              type="text"
              placeholder="Search by name or admission number..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-64 pr-10"
            />
            {searchQuery && (
              <button
                type="button"
                onClick={handleClearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            )}
          </div>

          <Select
            options={[
              { value: "All", label: "All Grades" },
              ...grades.map(grade => ({ value: grade.name, label: grade.name }))
            ]}
            value={selectedGrade}
            onChange={(value) => {
              setSelectedGrade(value);
              setCurrentPage(1);
            }}
            className="w-48"
          />

          <div className="flex gap-2 ml-auto">
            <Button
              className="bg-orange-300 hover:bg-orange-600 text-white-800"
              variant="outline"
              onClick={() => navigate('/inactive-students')}
            >
              View Inactive
            </Button>
            <Button
              className="bg-yellow-300 hover:bg-yellow-600 text-white-800"
              variant="outline"
              onClick={() => navigate('/unassigned-students')}
            >
              View Unassigned
            </Button>
            <Button
              className="bg-green-300 hover:bg-green-600 text-white-800"
              variant="secondary"
              onClick={handleExportExcel}
              disabled={loading}
            >
              {loading ? "Loading..." : "Export Excel"}
            </Button>
            <Button
              className="bg-red-300 hover:bg-red-600 text-white-800"
              variant="secondary"
              onClick={handleExportPDF}
              disabled={loading}
            >
              {loading ? "Loading..." : "Export PDF"}
            </Button>
          </div>
        </div>

        {/* Student List - Table or Cards View */}
        {viewMode === 'table' ? (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow key="header">
                  <TableCell isHeader>
                    <input
                      type="checkbox"
                      checked={selectedStudents.length === students.length && students.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </TableCell>
                  <TableCell isHeader>#</TableCell>
                  <TableCell isHeader>Student Info</TableCell>
                  <TableCell isHeader>Academic Info</TableCell>
                  <TableCell isHeader>Status</TableCell>
                  <TableCell isHeader>Actions</TableCell>
                </TableRow>
              </TableHeader>

              <TableBody>
                {loading ? (
                  <TableRow key="loading">
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
                      <p className="mt-2">Loading students...</p>
                    </TableCell>
                  </TableRow>
                ) : students.length === 0 ? (
                  <TableRow key="empty">
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="text-gray-500">
                        <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                        <p className="text-lg font-medium">No students found</p>
                        <p className="text-sm">
                          {selectedBranch?.id
                            ? `No students found in "${selectedBranch.name}" branch. Try selecting a different branch or clearing filters.`
                            : 'Try adjusting your search or filters'
                          }
                        </p>
                        {searchQuery && (
                          <button
                            type="button"
                            onClick={handleClearSearch}
                            className="mt-2 text-sm text-blue-600 hover:underline"
                          >
                            Clear search
                          </button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                students.map((student, index) => {
                  // Create a unique key combining multiple identifiers
                  const studentKey = `student-${student.id || 'no-id'}-${student.admission_number || 'no-admission'}-${index}`;
                  console.log(`Student ${index}: ID=${student.id}, Admission=${student.admission_number}, Key=${studentKey}`);

                  return (
                    <TableRow key={studentKey} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <TableCell>
                        <input
                          type="checkbox"
                          checked={selectedStudents.includes(student.id)}
                          onChange={() => handleSelectStudent(student.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        #{getStartingIndex() + index}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                            {student.first_name?.charAt(0)}{student.last_name?.charAt(0)}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {student.first_name} {student.last_name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {student.admission_number || 'No admission number'}
                            </div>
                            <div className="text-xs text-gray-400">
                              {student.gender === 'M' ? '👨 Male' : student.gender === 'F' ? '👩 Female' : '❓ Unknown'}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {student.grade || 'No Grade'}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600">
                            📚 {student.class_name || 'No Class'}
                          </div>
                          <div className="text-xs text-gray-500">
                            🏫 {student.stream_name || 'No Stream'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            student.user?.is_active !== false
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {student.user?.is_active !== false ? '✅ Active' : '❌ Inactive'}
                          </span>
                        </div>
                        {student.admission_date && (
                          <div className="text-xs text-gray-500 mt-1">
                            📅 {new Date(student.admission_date).toLocaleDateString()}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                      <div className="flex gap-2">
                        <button
                          type="button"
                          onClick={() => navigate(`/student/${student.id}`)}
                          className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                          title="View Details"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            console.log('🔍 Edit button clicked for student:', student);
                            console.log('🔍 Student ID for navigation:', student.id);
                            console.log('🔍 Student user ID:', student.user?.id);
                            console.log('🔍 Student admission number:', student.admission_number);

                            // Use student.id, fallback to user.id if needed
                            const studentId = student.id || student.user?.id;
                            console.log('🔍 Final ID for navigation:', studentId);
                            console.log('🔍 Navigating to:', `/edit-student/${studentId}`);

                            if (studentId) {
                              navigate(`/edit-student/${studentId}`);
                            } else {
                              console.error('❌ No valid student ID found for navigation');
                              alert('Error: Cannot edit student - no valid ID found');
                            }
                          }}
                          className="p-1 text-green-600 hover:bg-green-50 rounded"
                          title="Edit Student"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            const isActive = student.user?.is_active !== false;
                            const action = isActive ? 'deactivate' : 'reactivate';
                            const actionPast = isActive ? 'deactivated' : 'reactivated';

                            const confirmMessage = isActive
                              ? `⚠️ Deactivate Student\n\nAre you sure you want to deactivate ${student.first_name} ${student.last_name}?\n\nThis will remove their access to the system.`
                              : `🔄 Reactivate Student\n\nAre you sure you want to reactivate ${student.first_name} ${student.last_name}?\n\nThis will restore their access to the system.`;

                            if (window.confirm(confirmMessage)) {
                              studentService.toggleStudentStatus(student.id, !isActive)
                                .then(() => {
                                  fetchStudents();
                                  alert(`✅ Student ${actionPast} successfully!`);
                                })
                                .catch(err => {
                                  const error = err as { response?: { data?: { message?: string } }, message?: string };
                                  alert(`❌ Failed to ${action} student: ${error.response?.data?.message || error.message || 'Unknown error'}`);
                                });
                            }
                          }}
                          className={`p-1 rounded ${
                            student.user?.is_active !== false
                              ? 'text-orange-600 hover:bg-orange-50'
                              : 'text-green-600 hover:bg-green-50'
                          }`}
                          title={student.user?.is_active !== false ? 'Deactivate Student' : 'Reactivate Student'}
                        >
                          {student.user?.is_active !== false ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </TableCell>
                  </TableRow>
                  );
                })
              )}
              </TableBody>
            </Table>
          </div>
        ) : (
          /* Card View */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {loading ? (
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 rounded"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              ))
            ) : students.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                <p className="text-xl font-medium text-gray-500 mb-2">No students found</p>
                <p className="text-gray-400">
                  {selectedBranch?.id
                    ? `No students found in "${selectedBranch.name}" branch. Try selecting a different branch or clearing filters.`
                    : 'Try adjusting your search or filters'
                  }
                </p>
                {searchQuery && (
                  <button
                    type="button"
                    onClick={handleClearSearch}
                    className="mt-4 text-blue-600 hover:underline"
                  >
                    Clear search
                  </button>
                )}
              </div>
            ) : (
              students.map((student, index) => {
                const studentKey = `card-${student.id || 'no-id'}-${student.admission_number || 'no-admission'}-${index}`;

                return (
                  <div key={studentKey} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
                    {/* Card Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          checked={selectedStudents.includes(student.id)}
                          onChange={() => handleSelectStudent(student.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                          {student.first_name?.charAt(0)}{student.last_name?.charAt(0)}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {student.first_name} {student.last_name}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {student.admission_number || 'No admission number'}
                          </p>
                        </div>
                      </div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        student.user?.is_active !== false
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {student.user?.is_active !== false ? 'Active' : 'Inactive'}
                      </span>
                    </div>

                    {/* Card Content */}
                    <div className="space-y-3 mb-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Gender:</span>
                        <span className="text-sm font-medium">
                          {student.gender === 'M' ? '👨 Male' : student.gender === 'F' ? '👩 Female' : '❓ Unknown'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Grade:</span>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {student.grade || 'No Grade'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Class:</span>
                        <span className="text-sm font-medium">{student.class_name || 'No Class'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Stream:</span>
                        <span className="text-sm font-medium">{student.stream_name || 'No Stream'}</span>
                      </div>
                      {student.admission_date && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Admission:</span>
                          <span className="text-sm font-medium">{new Date(student.admission_date).toLocaleDateString()}</span>
                        </div>
                      )}
                    </div>

                    {/* Card Actions */}
                    <div className="flex gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <button
                        type="button"
                        onClick={() => navigate(`/student/${student.id}`)}
                        className="flex-1 px-3 py-2 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
                      >
                        View Details
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          const studentId = student.id || student.user?.id;
                          if (studentId) {
                            navigate(`/edit-student/${studentId}`);
                          } else {
                            alert('Error: Cannot edit student - no valid ID found');
                          }
                        }}
                        className="flex-1 px-3 py-2 text-sm bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors"
                      >
                        Edit
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          const isActive = student.user?.is_active !== false;
                          const action = isActive ? 'deactivate' : 'reactivate';
                          const actionPast = isActive ? 'deactivated' : 'reactivated';

                          const confirmMessage = isActive
                            ? `⚠️ Deactivate Student\n\nAre you sure you want to deactivate ${student.first_name} ${student.last_name}?\n\nThis will remove their access to the system.`
                            : `🔄 Reactivate Student\n\nAre you sure you want to reactivate ${student.first_name} ${student.last_name}?\n\nThis will restore their access to the system.`;

                          if (window.confirm(confirmMessage)) {
                            studentService.toggleStudentStatus(student.id, !isActive)
                              .then(() => {
                                fetchStudents();
                                alert(`✅ Student ${actionPast} successfully!`);
                              })
                              .catch(err => {
                                const error = err as { response?: { data?: { message?: string } }, message?: string };
                                alert(`❌ Failed to ${action} student: ${error.response?.data?.message || error.message || 'Unknown error'}`);
                              });
                          }
                        }}
                        className={`flex-1 px-3 py-2 text-sm rounded-lg transition-colors ${
                          student.user?.is_active !== false
                            ? 'bg-orange-50 text-orange-600 hover:bg-orange-100'
                            : 'bg-green-50 text-green-600 hover:bg-green-100'
                        }`}
                      >
                        {student.user?.is_active !== false ? 'Deactivate' : 'Reactivate'}
                      </button>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        )}

        {/* Pagination */}
        {students.length > 0 && (
          <div className="flex justify-between items-center mt-4">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Showing {students.length} of {totalStudents} students (Page {currentPage} of {totalPages})
            </span>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1 || loading}
                className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50 hover:bg-gray-300"
              >
                Previous
              </button>
              <button
                type="button"
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages || loading}
                className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50 hover:bg-gray-300"
              >
                Next
              </button>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
}
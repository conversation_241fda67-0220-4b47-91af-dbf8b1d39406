import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from 'react-redux';

import PageBreadcrumb from "../../components/common/PageBreadcrumb";
import PageMeta from "../../components/common/PageMeta";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "../../components/ui/table";
import Input from "../../components/form/input/InputField";
import Button from "../../components/ui/button/Button";

import { studentService, type Student } from "../../services/studentService";
import { RootState } from '../../store/store';

// Types for unassigned students analysis
interface UnassignedAnalysis {
  noSchool: Student[];
  noBranch: Student[];
  noSchoolNoBranch: Student[];
  total: number;
}

export default function UnassignedStudents() {
  const navigate = useNavigate();
  const [analysis, setAnalysis] = useState<UnassignedAnalysis>({
    noSchool: [],
    noBranch: [],
    noSchoolNoBranch: [],
    total: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'noSchool' | 'noBranch' | 'noSchoolNoBranch'>('all');
  const [selectedStudents, setSelectedStudents] = useState<number[]>([]);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');

  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    fetchUnassignedStudents();
  }, []);

  const fetchUnassignedStudents = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all students without any filters to analyze assignments
      const response = await studentService.getAllStudents({
        page_size: 1000 // Get all students
      });

      const allStudents = response.results || [];
      
      // Analyze student assignments
      const noSchool: Student[] = [];
      const noBranch: Student[] = [];
      const noSchoolNoBranch: Student[] = [];

      allStudents.forEach(student => {
        const hasSchool = student.school_branch?.school?.id;
        const hasBranch = student.school_branch?.id;

        if (!hasSchool && !hasBranch) {
          noSchoolNoBranch.push(student);
        } else if (!hasSchool) {
          noSchool.push(student);
        } else if (!hasBranch) {
          noBranch.push(student);
        }
      });

      const analysisData: UnassignedAnalysis = {
        noSchool,
        noBranch,
        noSchoolNoBranch,
        total: noSchool.length + noBranch.length + noSchoolNoBranch.length
      };

      setAnalysis(analysisData);

      console.log('📊 Unassigned Students Analysis:', analysisData);
      console.log(`📊 Total students analyzed: ${allStudents.length}`);
      console.log(`📊 Students with assignment issues: ${analysisData.total}`);

    } catch (err: unknown) {
      const error = err as { response?: { data?: { message?: string } }, message?: string };
      console.error('Error fetching unassigned students:', error);
      setError(error.response?.data?.message || "Failed to fetch unassigned students");
    } finally {
      setLoading(false);
    }
  };

  const getDisplayedStudents = (): Student[] => {
    let students: Student[] = [];
    
    switch (selectedCategory) {
      case 'noSchool':
        students = analysis.noSchool;
        break;
      case 'noBranch':
        students = analysis.noBranch;
        break;
      case 'noSchoolNoBranch':
        students = analysis.noSchoolNoBranch;
        break;
      default:
        students = [...analysis.noSchool, ...analysis.noBranch, ...analysis.noSchoolNoBranch];
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      students = students.filter(student =>
        student.first_name?.toLowerCase().includes(query) ||
        student.last_name?.toLowerCase().includes(query) ||
        student.admission_number?.toLowerCase().includes(query) ||
        student.user?.email?.toLowerCase().includes(query)
      );
    }

    return students;
  };

  const handleSelectStudent = (studentId: number) => {
    setSelectedStudents(prev =>
      prev.includes(studentId)
        ? prev.filter(id => id !== studentId)
        : [...prev, studentId]
    );
  };

  const handleSelectAll = () => {
    const displayedStudents = getDisplayedStudents();
    if (selectedStudents.length === displayedStudents.length) {
      setSelectedStudents([]);
    } else {
      setSelectedStudents(displayedStudents.map(s => s.id));
    }
  };

  const handleBulkAssign = () => {
    if (selectedStudents.length === 0) {
      alert('Please select students first');
      return;
    }
    
    // Navigate to bulk assignment page (to be created)
    navigate('/admin/bulk-assign-students', { 
      state: { studentIds: selectedStudents } 
    });
  };

  const handleExportCSV = () => {
    const students = getDisplayedStudents();
    const csvContent = generateCSV(students);
    downloadCSV(csvContent, `unassigned-students-${new Date().toISOString().split('T')[0]}.csv`);
  };

  const generateCSV = (students: Student[]) => {
    const headers = ['Name', 'Admission Number', 'Email', 'Status', 'Current School', 'Current Branch', 'Issue Type'];
    const rows = students.map(student => [
      `${student.first_name} ${student.last_name}`,
      student.admission_number || '',
      student.user?.email || '',
      student.user?.is_active ? 'Active' : 'Inactive',
      student.school_branch?.school?.name || 'Not Assigned',
      student.school_branch?.name || 'Not Assigned',
      getIssueType(student)
    ]);

    return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  };

  const downloadCSV = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getIssueType = (student: Student): string => {
    const hasSchool = student.school_branch?.school?.id;
    const hasBranch = student.school_branch?.id;

    if (!hasSchool && !hasBranch) return 'No School & No Branch';
    if (!hasSchool) return 'No School';
    if (!hasBranch) return 'No Branch';
    return 'Assigned';
  };

  const getIssueColor = (student: Student): string => {
    const issueType = getIssueType(student);
    switch (issueType) {
      case 'No School & No Branch': return 'bg-red-100 text-red-800 border-red-200';
      case 'No School': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'No Branch': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  const displayedStudents = getDisplayedStudents();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
          <p className="mt-2">Loading unassigned students...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageMeta title="Unassigned Students" description="Students without school or branch assignments" />
      
      <PageBreadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "Students", href: "/students" },
          { label: "Unassigned Students" }
        ]} 
      />

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Unassigned Students
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Students who need school or branch assignments
              </p>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => navigate('/students')}
              >
                ← Back to Students
              </Button>
              <Button
                variant="secondary"
                onClick={handleExportCSV}
                disabled={displayedStudents.length === 0}
              >
                Export CSV
              </Button>
              {selectedStudents.length > 0 && (
                <Button
                  onClick={handleBulkAssign}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Assign Selected ({selectedStudents.length})
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div 
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedCategory === 'all' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedCategory('all')}
            >
              <div className="text-2xl font-bold text-gray-900">{analysis.total}</div>
              <div className="text-sm text-gray-600">Total Issues</div>
            </div>
            
            <div 
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedCategory === 'noSchoolNoBranch' ? 'border-red-500 bg-red-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedCategory('noSchoolNoBranch')}
            >
              <div className="text-2xl font-bold text-red-600">{analysis.noSchoolNoBranch.length}</div>
              <div className="text-sm text-gray-600">No School & Branch</div>
            </div>
            
            <div 
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedCategory === 'noSchool' ? 'border-orange-500 bg-orange-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedCategory('noSchool')}
            >
              <div className="text-2xl font-bold text-orange-600">{analysis.noSchool.length}</div>
              <div className="text-sm text-gray-600">No School</div>
            </div>
            
            <div 
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedCategory === 'noBranch' ? 'border-yellow-500 bg-yellow-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedCategory('noBranch')}
            >
              <div className="text-2xl font-bold text-yellow-600">{analysis.noBranch.length}</div>
              <div className="text-sm text-gray-600">No Branch</div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                type="text"
                placeholder="Search by name, admission number, or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">View:</span>
              <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                <button
                  type="button"
                  onClick={() => setViewMode('table')}
                  className={`px-3 py-1 rounded text-sm flex items-center gap-1 ${viewMode === 'table' ? 'bg-white shadow text-gray-900' : 'text-gray-600'}`}
                >
                  Table
                </button>
                <button
                  type="button"
                  onClick={() => setViewMode('cards')}
                  className={`px-3 py-1 rounded text-sm flex items-center gap-1 ${viewMode === 'cards' ? 'bg-white shadow text-gray-900' : 'text-gray-600'}`}
                >
                  Cards
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="p-6">
          {error ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-4">
                <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-lg font-medium">Error Loading Data</p>
                <p className="text-sm">{error}</p>
              </div>
              <Button onClick={fetchUnassignedStudents}>
                Try Again
              </Button>
            </div>
          ) : displayedStudents.length === 0 ? (
            <div className="text-center py-8">
              <svg className="w-12 h-12 mx-auto mb-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-lg font-medium text-gray-900">
                {searchQuery ? 'No matching students found' : 'All students are properly assigned!'}
              </p>
              <p className="text-sm text-gray-600">
                {searchQuery ? 'Try adjusting your search criteria' : 'Every student has been assigned to a school and branch.'}
              </p>
            </div>
          ) : viewMode === 'table' ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableCell isHeader>
                      <input
                        type="checkbox"
                        checked={selectedStudents.length === displayedStudents.length && displayedStudents.length > 0}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300"
                      />
                    </TableCell>
                    <TableCell isHeader>Student</TableCell>
                    <TableCell isHeader>Contact</TableCell>
                    <TableCell isHeader>Current Assignment</TableCell>
                    <TableCell isHeader>Issue</TableCell>
                    <TableCell isHeader>Status</TableCell>
                    <TableCell isHeader>Actions</TableCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {displayedStudents.map((student) => (
                    <TableRow key={student.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <TableCell>
                        <input
                          type="checkbox"
                          checked={selectedStudents.includes(student.id)}
                          onChange={() => handleSelectStudent(student.id)}
                          className="rounded border-gray-300"
                        />
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900 dark:text-white">
                            {student.first_name} {student.last_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {student.admission_number || 'No admission number'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm text-gray-900">
                            {student.user?.email || 'No email'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {student.phone_number || 'No phone'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm">
                            <span className="font-medium">School:</span> {student.school_branch?.school?.name || 'Not assigned'}
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Branch:</span> {student.school_branch?.name || 'Not assigned'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getIssueColor(student)}`}>
                          {getIssueType(student)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          student.user?.is_active 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {student.user?.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <button
                            type="button"
                            onClick={() => navigate(`/student/${student.id}`)}
                            className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                            title="View Details"
                          >
                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          </button>
                          <button
                            type="button"
                            onClick={() => navigate(`/edit-student/${student.id}`)}
                            className="p-1 text-green-600 hover:bg-green-50 rounded"
                            title="Edit Student"
                          >
                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            /* Card View */
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {displayedStudents.map((student) => (
                <div key={student.id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={selectedStudents.includes(student.id)}
                        onChange={() => handleSelectStudent(student.id)}
                        className="rounded border-gray-300"
                      />
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {student.first_name} {student.last_name}
                        </h3>
                        <p className="text-sm text-gray-500">{student.admission_number || 'No admission number'}</p>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getIssueColor(student)}`}>
                      {getIssueType(student)}
                    </span>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Email:</span>
                      <span className="font-medium">{student.user?.email || 'Not provided'}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">School:</span>
                      <span className="font-medium">{student.school_branch?.school?.name || 'Not assigned'}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Branch:</span>
                      <span className="font-medium">{student.school_branch?.name || 'Not assigned'}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Status:</span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        student.user?.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {student.user?.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <button
                      type="button"
                      onClick={() => navigate(`/student/${student.id}`)}
                      className="flex-1 px-3 py-2 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
                    >
                      View Details
                    </button>
                    <button
                      type="button"
                      onClick={() => navigate(`/edit-student/${student.id}`)}
                      className="flex-1 px-3 py-2 text-sm bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors"
                    >
                      Edit
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

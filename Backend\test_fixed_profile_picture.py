#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_fixed_profile_picture():
    print("🔍 Testing Fixed Profile Picture & Email Update...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    headers = {'Authorization': f'Bearer {access_token}'}
    
    # Get a student for testing
    student = Student.objects.first()
    if not student:
        print("❌ No students found for testing")
        return
    
    print(f"\n📋 Testing with student: {student.user.first_name} {student.user.last_name}")
    print(f"   📋 Student User ID: {student.user.id}")
    print(f"   📋 Current email: {student.user.email}")
    print(f"   📋 Current profile picture: {getattr(student, 'profile_picture', 'None')}")
    
    # Test 1: Email Update with valid email
    print(f"\n🔍 Test 1: Email Update (Fixed)...")
    
    new_email = f"student{student.user.id}@shulexcel.com"
    email_data = {
        'email': new_email
    }
    
    student_url = f'http://localhost:8000/api/users/students/{student.user.id}/'
    
    try:
        print(f"   📡 Sending email update to: {student_url}")
        print(f"   📋 New email: {new_email}")
        
        response = requests.patch(student_url, json=email_data, headers=headers, timeout=10)
        print(f"   📡 Email Update Response: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ Email update successful!")
            
            # Verify in database
            student.user.refresh_from_db()
            db_email = student.user.email
            print(f"   📋 Database email: {db_email}")
            
            if db_email == new_email:
                print(f"   ✅ Email correctly updated in database!")
            else:
                print(f"   ❌ Email not updated in database")
                
        else:
            print(f"   ❌ Email update failed: {response.status_code}")
            print(f"   Error: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ Email update request failed: {str(e)}")
    
    # Test 2: Profile Picture Upload (Fixed)
    print(f"\n🔍 Test 2: Profile Picture Upload (Fixed)...")
    
    # Create a simple test image file
    test_image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    try:
        print(f"   📡 Sending profile picture upload to: {student_url}")
        
        # Test with multipart form data (file upload)
        files = {
            'profile_picture': ('test_profile.png', test_image_content, 'image/png')
        }
        
        # Remove Content-Type header for multipart upload
        upload_headers = {'Authorization': f'Bearer {access_token}'}
        
        response = requests.patch(student_url, files=files, headers=upload_headers, timeout=15)
        print(f"   📡 Profile Picture Upload Response: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ Profile picture upload successful!")
            
            # Check response data
            response_data = response.json()
            if 'profile_picture' in response_data:
                profile_picture_url = response_data['profile_picture']
                print(f"   📋 Response profile picture URL: {profile_picture_url}")
                if profile_picture_url:
                    print(f"   ✅ Profile picture URL returned!")
                else:
                    print(f"   ❌ Profile picture URL is empty")
            else:
                print(f"   ❌ Profile picture field not found in response")
                print(f"   📋 Response keys: {list(response_data.keys())}")
            
            # Verify in database
            student.refresh_from_db()
            db_profile_picture = getattr(student, 'profile_picture', None)
            print(f"   📋 Database profile picture: {db_profile_picture}")
            
            if db_profile_picture:
                print(f"   ✅ Profile picture saved in database!")
                print(f"   📋 File path: {db_profile_picture.name if hasattr(db_profile_picture, 'name') else 'No name'}")
                print(f"   📋 File URL: {db_profile_picture.url if hasattr(db_profile_picture, 'url') else 'No URL'}")
            else:
                print(f"   ❌ Profile picture not saved in database")
                
        else:
            print(f"   ❌ Profile picture upload failed: {response.status_code}")
            print(f"   Error: {response.text[:300]}")
            
    except Exception as e:
        print(f"   ❌ Profile picture upload request failed: {str(e)}")
    
    # Test 3: Retrieve student data to check both fields
    print(f"\n🔍 Test 3: Retrieving updated student data...")
    
    try:
        response = requests.get(student_url, headers=headers, timeout=10)
        print(f"   📡 GET Response: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Data retrieval successful!")
            
            # Check email in response
            email_in_response = None
            if 'user' in data and 'email' in data['user']:
                email_in_response = data['user']['email']
            elif 'email' in data:
                email_in_response = data['email']
            
            print(f"   📋 Email in GET response: {email_in_response}")
            
            # Check profile picture in response
            profile_picture_in_response = data.get('profile_picture')
            print(f"   📋 Profile picture in GET response: {profile_picture_in_response}")
            
            # Summary
            print(f"\n   📊 Field Status Summary:")
            print(f"      - Email field present: {'✅' if email_in_response else '❌'}")
            print(f"      - Profile picture field present: {'✅' if profile_picture_in_response else '❌'}")
            print(f"      - Profile picture has URL: {'✅' if profile_picture_in_response and profile_picture_in_response.startswith('/') else '❌'}")
            
        else:
            print(f"   ❌ Data retrieval failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ GET request failed: {str(e)}")
    
    print(f"\n🎯 Fixed profile picture and email test complete!")
    print(f"\n📊 Summary:")
    print(f"   - Email Update: {'✅' if response.status_code == 200 else '❌'}")
    print(f"   - Profile Picture Upload: {'✅' if response.status_code == 200 else '❌'}")
    print(f"   - Profile Picture in Response: {'✅' if 'profile_picture' in response_data else '❌'}")
    print(f"   - Profile Picture in Database: {'✅' if db_profile_picture else '❌'}")
    
    if response.status_code == 200 and 'profile_picture' in response_data and db_profile_picture:
        print(f"\n🎉 Both email and profile picture functionality are working!")
        print(f"✅ Students can now update their email and upload profile pictures!")
    else:
        print(f"\n❌ Some issues remain. Check the details above.")

if __name__ == "__main__":
    test_fixed_profile_picture()

#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from core.models import CustomUser
from schools.models import School, SchoolBranch
from users.models import Student, Teacher

def assign_users_to_sample_school():
    print("🚀 Assigning existing users to Sample School...")
    
    # Get Sample School and Main Campus
    try:
        sample_school = School.objects.get(name="Sample School")
        main_campus = SchoolBranch.objects.get(school=sample_school, name="Main Campus")
        print(f"✅ Found School: {sample_school.name}")
        print(f"✅ Found Branch: {main_campus.name}")
    except (School.DoesNotExist, SchoolBranch.DoesNotExist):
        print("❌ Sample School or Main Campus not found!")
        return
    
    # Get existing student and teacher profiles from MAKINI SCHOOL
    makini_school = School.objects.get(name="MAKINI SCHOOL")
    makini_branch = SchoolBranch.objects.get(school=makini_school, name="MAKINI SCHOOL - MAIN")
    
    # Move some students to Sample School
    students_to_move = Student.objects.filter(school_branch=makini_branch)[:3]
    students_moved = 0
    for student in students_to_move:
        student.school_branch = main_campus
        student.save()
        students_moved += 1
        print(f"✅ Moved student: {student.user.get_full_name()}")
    
    # Move some teachers to Sample School
    teachers_to_move = Teacher.objects.filter(school_branch=makini_branch)[:2]
    teachers_moved = 0
    for teacher in teachers_to_move:
        teacher.school_branch = main_campus
        teacher.save()
        teachers_moved += 1
        print(f"✅ Moved teacher: {teacher.user.get_full_name()}")
    
    print(f"\n📊 Users moved successfully!")
    print(f"🏫 Target School: {sample_school.name}")
    print(f"🏢 Target Branch: {main_campus.name}")
    print(f"👨‍🎓 Students moved: {students_moved}")
    print(f"👨‍🏫 Teachers moved: {teachers_moved}")
    
    # Verification
    print(f"\n=== VERIFICATION ===")
    total_students_in_branch = Student.objects.filter(school_branch=main_campus).count()
    total_teachers_in_branch = Teacher.objects.filter(school_branch=main_campus).count()
    print(f"✅ Total students in {main_campus.name}: {total_students_in_branch}")
    print(f"✅ Total teachers in {main_campus.name}: {total_teachers_in_branch}")

if __name__ == "__main__":
    assign_users_to_sample_school()

import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import { studentService, type Student, type StudentFormData } from "../../services/studentService";
import academicsService, { type Grade } from "../../services/academicsService.ts";
import axiosInstance from "../../components/utils/AxiosInstanceFixed";
import Button from "../../components/ui/button/Button";
import Input from "../../components/form/input/InputField";
import Label from "../../components/form/Label";
import Select from "../../components/form/Select";
import { useSelector } from "react-redux";
import { RootState } from "../../store/store";
import { useSchool } from "../../contexts/SchoolContext";

export default function EditStudent() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);
  const { selectedSchool, selectedBranch } = useSchool();

  const [formData, setFormData] = useState<StudentFormData>({
    // User information
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    is_active: true,

    // Personal information
    gender: "M",
    date_of_birth: "",
    address: "",
    phone_number: "",
    emergency_contact: "",
    additional_notes: "",

    // Academic information
    admission_number: "",
    admission_date: "",
    class_name: "",
    grade: "",
    stream: "",
    current_class: undefined,
    class_teacher: undefined,
    subjects: [],
    subject_type: "",

    // School information
    school_branch_id: selectedBranch?.id,
    parent: undefined,

    // Documents
    profile_picture: null,
    birth_certificate: null,
    result_slip: null,
    admission_letter: null,
  });

  const [student, setStudent] = useState<Student | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [grades, setGrades] = useState<Grade[]>([]);
  const [schools, setSchools] = useState<any[]>([]);
  const [branches, setBranches] = useState<any[]>([]);

  useEffect(() => {
    const fetchStudent = async () => {
      console.log('🔍 EditStudent - URL ID parameter:', id);
      console.log('🔍 EditStudent - Parsed ID:', parseInt(id || ''));

      if (!id) {
        console.error('❌ No ID parameter found in URL');
        setError('No student ID provided');
        setLoading(false);
        return;
      }

      const parsedId = parseInt(id);
      if (isNaN(parsedId)) {
        console.error('❌ Invalid ID parameter:', id);
        setError('Invalid student ID');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        console.log('📡 Fetching student with ID:', parsedId);
        const data = await studentService.getStudentById(parsedId);
        setStudent(data);

        // Initialize form data with student data
        const initialFormData = {
          // User information
          first_name: data.first_name || "",
          last_name: data.last_name || "",
          email: data.email || "",
          phone: data.phone || "",
          is_active: data.is_active !== undefined ? data.is_active : true,

          // Personal information
          gender: data.gender || "M",
          date_of_birth: data.date_of_birth || "",
          address: data.address || "",
          phone_number: data.phone_number || "",
          emergency_contact: data.emergency_contact || "",
          additional_notes: data.additional_notes || "",

          // Academic information
          admission_number: data.admission_number || "",
          admission_date: data.admission_date || "",
          class_name: data.class_name || "",
          grade: data.grade || "",
          stream: typeof data.stream === 'string' ? data.stream : "",
          stream_name: data.stream_name || "",
          current_class: data.current_class,
          class_teacher: data.class_teacher,
          subjects: data.subjects || [],
          subject_type: data.subject_type || "",

          // School information - handle nested school_branch object
          school_branch_id: data.school_branch_id || data.school_branch?.id || null,
          parent: data.parent,

          // Documents (files will be handled separately)
          profile_picture: null,
          birth_certificate: null,
          result_slip: null,
          admission_letter: null,
        };

        console.log('🔍 Initializing form with student data:', data);
        console.log('🔍 Student school_branch object:', data.school_branch);
        console.log('🔍 Student school_branch_id field:', data.school_branch_id);
        console.log('🔍 Extracted school_branch_id:', data.school_branch_id || data.school_branch?.id || null);
        console.log('🔍 Initial form data:', initialFormData);
        setFormData(initialFormData);

        // Set preview image if available
        if (data.profile_picture) {
          setPreviewImage(data.profile_picture);
        }
      } catch (err: unknown) {
        const error = err as { response?: { data?: { message?: string } }, message?: string };
        console.error('Error:', error.response?.data || error.message);
        setError(error.response?.data?.message || "Failed to fetch student details");
      } finally {
        setLoading(false);
      }
    };

    fetchStudent();
  }, [id, selectedBranch?.id]);

  useEffect(() => {
    // Fetch grades when component mounts
    const fetchGrades = async () => {
      try {
        // TODO: Fix academicsService.getGrades function
        // For now, use hardcoded grades to prevent errors
        const gradesData = [
          { name: 'Grade 1' },
          { name: 'Grade 2' },
          { name: 'Grade 3' },
          { name: 'Grade 4' },
          { name: 'Grade 5' },
          { name: 'Grade 6' },
          { name: 'Grade 7' },
          { name: 'Grade 8' },
          { name: 'Form 1' },
          { name: 'Form 2' },
          { name: 'Form 3' },
          { name: 'Form 4' }
        ];
        setGrades(gradesData);
        console.log('✅ Grades loaded (hardcoded) for EditStudent:', gradesData);
      } catch (error) {
        console.error('Error fetching grades:', error);
      }
    };

    fetchGrades();
  }, []);

  useEffect(() => {
    // Fetch schools and branches when component mounts
    const fetchSchoolsAndBranches = async () => {
      try {
        // Fetch schools using axios instance with proper authentication
        const schoolsResponse = await axiosInstance.get('/api/schools/schools/');
        setSchools(schoolsResponse.data.results || []);
        console.log('✅ Schools loaded for EditStudent:', schoolsResponse.data.results?.length || 0);

        // Fetch all branches using axios instance with proper authentication
        const branchesResponse = await axiosInstance.get('/api/schools/branch/');
        setBranches(branchesResponse.data.results || []);
        console.log('✅ Branches loaded for EditStudent:', branchesResponse.data.results?.length || 0);
        console.log('🔍 Sample branches:', branchesResponse.data.results?.slice(0, 2));
      } catch (error: any) {
        console.error('Error fetching schools/branches:', error);
        console.error('Error details:', error.response?.data || error.message);
      }
    };

    fetchSchoolsAndBranches();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    console.log(`🔍 Field changed: ${name} = ${value}`);
    setFormData(prev => {
      const newData = { ...prev, [name]: value };
      console.log('🔍 Updated formData:', newData);
      return newData;
    });
  };

  const handleSelectChange = (name: string) => (value: string) => {
    console.log(`🔍 Select changed: ${name} = ${value}`);
    setFormData(prev => {
      const newData = { ...prev, [name]: value };
      console.log('🔍 Updated formData:', newData);
      return newData;
    });
  };

  const handleSchoolBranchChange = (branchId: string) => {
    const branchIdNum = parseInt(branchId);
    console.log(`🔍 School branch changed: ${branchId}`);
    setFormData(prev => {
      const newData = { ...prev, school_branch_id: branchIdNum };
      console.log('🔍 Updated formData with school_branch_id:', newData);
      return newData;
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, profile_picture: file }));

      // Create a preview URL for the selected image
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id) return;

    console.log('🔍 Form submitted with data:', formData);
    console.log('🔍 Student ID:', id);

    try {
      setSaving(true);
      setError(null);

      await studentService.updateStudent(parseInt(id), formData);
      navigate(`/student/${id}`);
    } catch (err: unknown) {
      const error = err as { response?: { data?: any }, message?: string };
      console.error('❌ Update Error Details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        fullResponse: error.response
      });

      // Log specific error fields
      if (error.response?.data) {
        console.error('❌ Detailed Error Data:', JSON.stringify(error.response.data, null, 2));

        // Check for field-specific errors
        Object.keys(error.response.data).forEach(field => {
          if (Array.isArray(error.response.data[field])) {
            console.error(`❌ Field '${field}' errors:`, error.response.data[field]);
          }
        });
      }

      // Extract specific error messages
      let errorMessage = "Failed to update student";
      if (error.response?.data) {
        if (error.response.data.non_field_errors) {
          errorMessage = error.response.data.non_field_errors.join(', ');
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        }
      }

      setError(errorMessage);
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
        <p className="ml-4">Loading student details...</p>
      </div>
    );
  }

  if (error && !student) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-600 p-4 rounded">
        <h3 className="font-medium">Error loading student details</h3>
        <p>{error}</p>
        <button
          type="button"
          onClick={() => navigate('/AllStudents')}
          className="mt-2 text-sm text-red-800 underline"
        >
          Back to Students List
        </button>
      </div>
    );
  }

  return (
    <div>
      <PageMeta title={`Edit ${student?.first_name || 'Student'} | ShuleXcel`} description="Edit student details." />
      <PageBreadcrumb pageTitle="Edit Student" />

      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03] p-6">
        {/* Header */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-800 text-theme-xl dark:text-white/90 sm:text-2xl">
            Edit Student Information
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Update the student's information below.
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 p-4 rounded mb-6">
            <h3 className="font-medium">Error</h3>
            <p>{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Personal Information Section */}
          <div className="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-lg">
            <h4 className="font-medium text-gray-700 dark:text-white/80 mb-6 text-lg border-b pb-2">
              👤 Personal Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <Label>First Name <span className="text-red-500">*</span></Label>
                <Input
                  type="text"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <Label>Last Name <span className="text-red-500">*</span></Label>
                <Input
                  type="text"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <Label>Gender <span className="text-red-500">*</span></Label>
                <Select
                  options={[
                    { value: "M", label: "Male" },
                    { value: "F", label: "Female" },
                  ]}
                  value={formData.gender}
                  onChange={handleSelectChange("gender")}
                />
              </div>

              <div>
                <Label>Date of Birth</Label>
                <Input
                  type="date"
                  name="date_of_birth"
                  value={formData.date_of_birth}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label>Email Address</Label>
                <Input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label>Phone Number</Label>
                <Input
                  type="tel"
                  name="phone_number"
                  value={formData.phone_number}
                  onChange={handleChange}
                  placeholder="+254712345678"
                />
              </div>

              <div className="md:col-span-2 lg:col-span-3">
                <Label>Home Address</Label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  rows={3}
                  placeholder="Enter student's home address"
                />
              </div>

              <div>
                <Label>Emergency Contact</Label>
                <Input
                  type="tel"
                  name="emergency_contact"
                  value={formData.emergency_contact}
                  onChange={handleChange}
                  placeholder="+254712345678"
                />
              </div>
            </div>
          </div>

          {/* Academic Information Section */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
            <h4 className="font-medium text-gray-700 dark:text-white/80 mb-6 text-lg border-b pb-2">
              🎓 Academic Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <Label>Admission Number <span className="text-red-500">*</span></Label>
                <Input
                  type="text"
                  name="admission_number"
                  value={formData.admission_number}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <Label>Admission Date</Label>
                <Input
                  type="date"
                  name="admission_date"
                  value={formData.admission_date}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label>Grade <span className="text-red-500">*</span></Label>
                <Select
                  options={grades.map(grade => ({ value: grade.name, label: grade.name }))}
                  value={formData.grade}
                  onChange={handleSelectChange("grade")}
                />
              </div>

              <div>
                <Label>Class <span className="text-red-500">*</span></Label>
                <Input
                  type="text"
                  name="class_name"
                  value={formData.class_name}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <Label>Stream</Label>
                <Input
                  type="text"
                  name="stream"
                  value={formData.stream}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label>Subject Type</Label>
                <Select
                  options={[
                    { value: "", label: "Select Subject Type" },
                    { value: "Compulsory", label: "Compulsory Subjects" },
                    { value: "Optional", label: "Optional Subjects" },
                  ]}
                  value={formData.subject_type}
                  onChange={handleSelectChange("subject_type")}
                />
              </div>
            </div>
          </div>

          {/* School Assignment Section */}
          <div className="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg">
            <h4 className="font-medium text-gray-700 dark:text-white/80 mb-6 text-lg border-b pb-2">
              🏫 School Assignment
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label>Current School Branch <span className="text-red-500">*</span></Label>
                <Select
                  options={[
                    { value: "", label: "Select School Branch" },
                    ...branches.map(branch => {
                      // Find school name from schools array
                      const school = schools.find(s => s.id === branch.school);
                      const schoolName = school?.name || branch.school_name || 'Unknown School';
                      return {
                        value: branch.id.toString(),
                        label: `${branch.name} (${schoolName})`
                      };
                    })
                  ]}
                  value={formData.school_branch_id?.toString() || ""}
                  onChange={handleSchoolBranchChange}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Assign the student to a specific school branch
                </p>
              </div>

              <div>
                <Label>Assignment Status</Label>
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                  {formData.school_branch_id ? (
                    <div className="flex items-center gap-2 text-green-600">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm font-medium">Assigned</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-orange-600">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm font-medium">Unassigned</span>
                    </div>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.school_branch_id
                      ? 'Student is assigned to a school branch'
                      : 'Student needs to be assigned to a school branch'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Documents Section */}
          <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
            <h4 className="font-medium text-gray-700 dark:text-white/80 mb-6 text-lg border-b pb-2">
              📄 Documents & Files
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label>Profile Picture</Label>
                <div className="flex flex-col gap-4">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                  />
                  {previewImage && (
                    <div className="w-32 h-32 overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                      <img
                        src={previewImage}
                        alt="Profile Preview"
                        className="object-cover w-full h-full"
                      />
                    </div>
                  )}
                </div>
              </div>

              <div>
                <Label>Birth Certificate</Label>
                <input
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      setFormData(prev => ({ ...prev, birth_certificate: file }));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                />
              </div>

              <div>
                <Label>Previous School Result Slip</Label>
                <input
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      setFormData(prev => ({ ...prev, result_slip: file }));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                />
              </div>

              <div>
                <Label>Admission Letter</Label>
                <input
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      setFormData(prev => ({ ...prev, admission_letter: file }));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                />
              </div>
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg">
            <h4 className="font-medium text-gray-700 dark:text-white/80 mb-6 text-lg border-b pb-2">
              📝 Additional Information
            </h4>
            <div>
              <Label>Additional Notes</Label>
              <textarea
                name="additional_notes"
                value={formData.additional_notes}
                onChange={(e) => setFormData(prev => ({ ...prev, additional_notes: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                rows={4}
                placeholder="Any additional information about the student (medical conditions, special needs, achievements, etc.)"
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 mt-8">
            <Button
              variant="secondary"
              type="button"
              onClick={() => navigate(`/student/${id}`)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              type="submit"
              disabled={saving}
            >
              {saving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
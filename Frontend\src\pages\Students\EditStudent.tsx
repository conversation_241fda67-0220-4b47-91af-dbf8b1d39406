import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import { studentService, type Student, type StudentFormData } from "../../services/studentService";
import academicsService, { type Grade } from "../../services/academicsService.ts";
import Button from "../../components/ui/button/Button";
import Input from "../../components/form/input/InputField";
import Label from "../../components/form/Label";
import Select from "../../components/form/Select";
import { useSelector } from "react-redux";
import { RootState } from "../../store/store";
import { useSchool } from "../../contexts/SchoolContext";

export default function EditStudent() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);
  const { selectedSchool, selectedBranch } = useSchool();

  const [formData, setFormData] = useState<StudentFormData>({
    first_name: "",
    last_name: "",
    gender: "M",
    date_of_birth: "",
    admission_number: "",
    class_name: "",
    grade: "",
    stream: "",
    school_branch_id: selectedBranch?.id,
    profile_picture: null,
  });

  const [student, setStudent] = useState<Student | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [grades, setGrades] = useState<Grade[]>([]);

  useEffect(() => {
    const fetchStudent = async () => {
      console.log('🔍 EditStudent - URL ID parameter:', id);
      console.log('🔍 EditStudent - Parsed ID:', parseInt(id || ''));

      if (!id) {
        console.error('❌ No ID parameter found in URL');
        setError('No student ID provided');
        setLoading(false);
        return;
      }

      const parsedId = parseInt(id);
      if (isNaN(parsedId)) {
        console.error('❌ Invalid ID parameter:', id);
        setError('Invalid student ID');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        console.log('📡 Fetching student with ID:', parsedId);
        const data = await studentService.getStudentById(parsedId);
        setStudent(data);

        // Initialize form data with student data
        setFormData({
          first_name: data.first_name,
          last_name: data.last_name,
          gender: data.gender || "M",
          date_of_birth: data.date_of_birth || "",
          admission_number: data.admission_number,
          class_name: data.class_name,
          grade: data.grade,
          stream: typeof data.stream === 'string' ? data.stream : "",
          stream_name: data.stream_name || "",
          school_branch_id: data.school_branch_id,
          profile_picture: null,
        });

        // Set preview image if available
        if (data.profile_picture) {
          setPreviewImage(data.profile_picture);
        }
      } catch (err: unknown) {
        const error = err as { response?: { data?: { message?: string } }, message?: string };
        console.error('Error:', error.response?.data || error.message);
        setError(error.response?.data?.message || "Failed to fetch student details");
      } finally {
        setLoading(false);
      }
    };

    fetchStudent();
  }, [id, selectedBranch?.id]);

  useEffect(() => {
    // Fetch grades when component mounts
    const fetchGrades = async () => {
      try {
        // TODO: Fix academicsService.getGrades function
        // For now, use hardcoded grades to prevent errors
        const gradesData = [
          { name: 'Grade 1' },
          { name: 'Grade 2' },
          { name: 'Grade 3' },
          { name: 'Grade 4' },
          { name: 'Grade 5' },
          { name: 'Grade 6' },
          { name: 'Grade 7' },
          { name: 'Grade 8' },
          { name: 'Form 1' },
          { name: 'Form 2' },
          { name: 'Form 3' },
          { name: 'Form 4' }
        ];
        setGrades(gradesData);
        console.log('✅ Grades loaded (hardcoded) for EditStudent:', gradesData);
      } catch (error) {
        console.error('Error fetching grades:', error);
      }
    };

    fetchGrades();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string) => (value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, profile_picture: file }));

      // Create a preview URL for the selected image
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id) return;

    try {
      setSaving(true);
      setError(null);

      await studentService.updateStudent(parseInt(id), formData);
      navigate(`/student/${id}`);
    } catch (err: unknown) {
      const error = err as { response?: { data?: { message?: string } }, message?: string };
      console.error('Error:', error.response?.data || error.message);
      setError(error.response?.data?.message || "Failed to update student");
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
        <p className="ml-4">Loading student details...</p>
      </div>
    );
  }

  if (error && !student) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-600 p-4 rounded">
        <h3 className="font-medium">Error loading student details</h3>
        <p>{error}</p>
        <button
          type="button"
          onClick={() => navigate('/AllStudents')}
          className="mt-2 text-sm text-red-800 underline"
        >
          Back to Students List
        </button>
      </div>
    );
  }

  return (
    <div>
      <PageMeta title={`Edit ${student?.first_name || 'Student'} | ShuleXcel`} description="Edit student details." />
      <PageBreadcrumb pageTitle="Edit Student" />

      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03] p-6">
        {/* Header */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-800 text-theme-xl dark:text-white/90 sm:text-2xl">
            Edit Student Information
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Update the student's information below.
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 p-4 rounded mb-6">
            <h3 className="font-medium">Error</h3>
            <p>{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <div className="md:col-span-2">
              <h4 className="font-medium text-gray-700 dark:text-white/80 mb-4 border-b pb-2">Personal Information</h4>
            </div>

            <div>
              <Label>First Name <span className="text-red-500">*</span></Label>
              <Input
                type="text"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <Label>Last Name <span className="text-red-500">*</span></Label>
              <Input
                type="text"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <Label>Gender <span className="text-red-500">*</span></Label>
              <Select
                options={[
                  { value: "M", label: "Male" },
                  { value: "F", label: "Female" },
                ]}
                value={formData.gender}
                onChange={handleSelectChange("gender")}
              />
            </div>

            <div>
              <Label>Date of Birth</Label>
              <Input
                type="date"
                name="date_of_birth"
                value={formData.date_of_birth}
                onChange={handleChange}
              />
            </div>

            {/* Academic Information */}
            <div className="md:col-span-2 mt-4">
              <h4 className="font-medium text-gray-700 dark:text-white/80 mb-4 border-b pb-2">Academic Information</h4>
            </div>

            <div>
              <Label>Admission Number <span className="text-red-500">*</span></Label>
              <Input
                type="text"
                name="admission_number"
                value={formData.admission_number}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <Label>Grade <span className="text-red-500">*</span></Label>
              <Select
                options={grades.map(grade => ({ value: grade.name, label: grade.name }))}
                value={formData.grade}
                onChange={handleSelectChange("grade")}
              />
            </div>

            <div>
              <Label>Class <span className="text-red-500">*</span></Label>
              <Input
                type="text"
                name="class_name"
                value={formData.class_name}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <Label>Stream</Label>
              <Input
                type="text"
                name="stream"
                value={formData.stream}
                onChange={handleChange}
              />
            </div>

            {/* Profile Picture */}
            <div className="md:col-span-2 mt-4">
              <h4 className="font-medium text-gray-700 dark:text-white/80 mb-4 border-b pb-2">Profile Picture</h4>
            </div>

            <div className="md:col-span-2">
              <div className="flex flex-col md:flex-row gap-6 items-start">
                <div className="w-40 h-40 overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                  {previewImage ? (
                    <img
                      src={previewImage}
                      alt="Profile Preview"
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <div className="text-gray-400 text-center p-4">
                      No image selected
                    </div>
                  )}
                </div>

                <div className="flex-1">
                  <Label>Upload New Picture</Label>
                  <Input
                    type="file"
                    name="profile_picture"
                    onChange={handleFileChange}
                    accept="image/*"
                  />
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                    Recommended size: 300x300 pixels. Max file size: 2MB.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 mt-8">
            <Button
              variant="secondary"
              type="button"
              onClick={() => navigate(`/student/${id}`)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              type="submit"
              disabled={saving}
            >
              {saving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
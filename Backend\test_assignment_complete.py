#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student, SchoolBranch
from core.models import CustomUser

def test_assignment_complete():
    print("🎯 Testing Complete School Assignment Workflow...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    headers = {'Authorization': f'Bearer {access_token}'}
    
    # Step 1: Test frontend data fetching (simulating EditStudent page)
    print(f"\n📋 Step 1: Testing Frontend Data Fetching...")
    
    # Fetch schools
    schools_response = requests.get('http://localhost:8000/api/schools/schools/', headers=headers, timeout=10)
    schools_data = schools_response.json() if schools_response.status_code == 200 else {'results': []}
    schools = schools_data.get('results', [])
    print(f"   ✅ Schools loaded: {len(schools)} schools")
    
    # Fetch branches
    branches_response = requests.get('http://localhost:8000/api/schools/branch/', headers=headers, timeout=10)
    branches_data = branches_response.json() if branches_response.status_code == 200 else {'results': []}
    branches = branches_data.get('results', [])
    print(f"   ✅ Branches loaded: {len(branches)} branches")
    
    # Step 2: Simulate dropdown options generation
    print(f"\n📋 Step 2: Testing Dropdown Options Generation...")
    dropdown_options = []
    for branch in branches:
        # Find school name from schools array (simulating frontend logic)
        school = next((s for s in schools if s['id'] == branch['school']), None)
        school_name = school['name'] if school else branch.get('school_name', 'Unknown School')
        option = {
            'value': str(branch['id']),
            'label': f"{branch['name']} ({school_name})"
        }
        dropdown_options.append(option)
        
    print(f"   ✅ Generated {len(dropdown_options)} dropdown options:")
    for i, option in enumerate(dropdown_options[:3]):  # Show first 3
        print(f"      {i+1}. {option['label']} (value: {option['value']})")
    if len(dropdown_options) > 3:
        print(f"      ... and {len(dropdown_options) - 3} more")
    
    # Step 3: Test student assignment
    print(f"\n📋 Step 3: Testing Student Assignment...")
    
    # Get an unassigned student
    unassigned_student = Student.objects.filter(school_branch__isnull=True).first()
    if not unassigned_student:
        print("   ⚠️ No unassigned students found, using first student")
        unassigned_student = Student.objects.first()
    
    if not unassigned_student:
        print("   ❌ No students found for testing")
        return
    
    print(f"   📋 Testing with: {unassigned_student.user.first_name} {unassigned_student.user.last_name}")
    print(f"   📋 Current assignment: {unassigned_student.school_branch}")
    
    # Get a branch to assign to
    target_branch = SchoolBranch.objects.first()
    if not target_branch:
        print("   ❌ No branches found for assignment")
        return
    
    print(f"   🎯 Target assignment: {target_branch.name} (ID: {target_branch.id})")
    
    # Perform assignment
    assignment_url = f'http://localhost:8000/api/users/students/{unassigned_student.user.id}/'
    assignment_data = {'school_branch_id': target_branch.id}
    
    assignment_response = requests.patch(assignment_url, json=assignment_data, headers=headers, timeout=10)
    
    if assignment_response.status_code == 200:
        print(f"   ✅ Assignment successful!")
        
        # Verify assignment
        unassigned_student.refresh_from_db()
        if unassigned_student.school_branch and unassigned_student.school_branch.id == target_branch.id:
            print(f"   ✅ Assignment verified in database!")
        else:
            print(f"   ❌ Assignment not reflected in database")
    else:
        print(f"   ❌ Assignment failed: {assignment_response.status_code}")
        print(f"   Error: {assignment_response.text[:200]}")
    
    # Step 4: Test student data retrieval (simulating form loading)
    print(f"\n📋 Step 4: Testing Student Data Retrieval...")
    
    student_url = f'http://localhost:8000/api/users/students/{unassigned_student.user.id}/'
    student_response = requests.get(student_url, headers=headers, timeout=10)
    
    if student_response.status_code == 200:
        student_data = student_response.json()
        school_branch = student_data.get('school_branch')
        school_branch_id = student_data.get('school_branch_id')
        
        print(f"   ✅ Student data retrieved successfully")
        print(f"   📋 school_branch field: {type(school_branch)} - {school_branch.get('name') if isinstance(school_branch, dict) else school_branch}")
        print(f"   📋 school_branch_id field: {school_branch_id}")
        
        # Extract school_branch_id (simulating frontend logic)
        extracted_id = school_branch_id or (school_branch.get('id') if isinstance(school_branch, dict) else None)
        print(f"   📋 Extracted school_branch_id: {extracted_id}")
        
        if extracted_id == target_branch.id:
            print(f"   ✅ Form would correctly show assigned branch!")
        else:
            print(f"   ❌ Form would not show correct assignment")
    else:
        print(f"   ❌ Student data retrieval failed: {student_response.status_code}")
    
    print(f"\n🎉 Complete workflow test finished!")
    print(f"📊 Summary:")
    print(f"   - Schools API: {'✅' if len(schools) > 0 else '❌'}")
    print(f"   - Branches API: {'✅' if len(branches) > 0 else '❌'}")
    print(f"   - Dropdown generation: {'✅' if len(dropdown_options) > 0 else '❌'}")
    print(f"   - Student assignment: {'✅' if assignment_response.status_code == 200 else '❌'}")
    print(f"   - Data retrieval: {'✅' if student_response.status_code == 200 else '❌'}")

if __name__ == "__main__":
    test_assignment_complete()

#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from schools.models import School, SchoolBranch
from users.models import Student, Teacher, Staff, AdminProfile, Parent

def verify_user_counts_data():
    print("🔍 Verifying User Counts Data by School/Branch...")
    
    # Get all schools and branches
    schools = School.objects.all()
    
    for school in schools:
        print(f"\n🏫 School: {school.name} (ID: {school.id})")
        
        # Get branches for this school
        branches = SchoolBranch.objects.filter(school=school)
        
        for branch in branches:
            print(f"  🏢 Branch: {branch.name} (ID: {branch.id})")
            
            # Count users in this branch
            students = Student.objects.filter(school_branch=branch).count()
            teachers = Teacher.objects.filter(school_branch=branch).count()
            staff = Staff.objects.filter(school_branch=branch).count()
            admins = AdminProfile.objects.filter(school_branch=branch).count()
            parents = Parent.objects.filter(school_branch=branch).distinct().count()
            
            total = students + teachers + staff + admins + parents
            
            print(f"    👨‍🎓 Students: {students}")
            print(f"    👨‍🏫 Teachers: {teachers}")
            print(f"    👨‍💼 Staff: {staff}")
            print(f"    👨‍💻 Admins: {admins}")
            print(f"    👨‍👩‍👧‍👦 Parents: {parents}")
            print(f"    📊 Total: {total}")
            
            if total > 0:
                print(f"    ✅ Has users")
            else:
                print(f"    ❌ No users")
    
    print(f"\n=== SUMMARY ===")
    print(f"📊 Global Totals:")
    print(f"   👨‍🎓 Students: {Student.objects.count()}")
    print(f"   👨‍🏫 Teachers: {Teacher.objects.count()}")
    print(f"   👨‍💼 Staff: {Staff.objects.count()}")
    print(f"   👨‍💻 Admins: {AdminProfile.objects.count()}")
    print(f"   👨‍👩‍👧‍👦 Parents: {Parent.objects.count()}")

if __name__ == "__main__":
    verify_user_counts_data()

// Token Migration Script for ShuleXcel Frontend
// Run this in the browser console to fix authentication issues

console.log('🔧 ShuleXcel Token Migration Script');
console.log('===================================');

// Valid tokens from backend test
const VALID_ACCESS_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzODI2MzA3LCJpYXQiOjE3NTM4MTkxMDcsImp0aSI6ImEzYzI5ZTk5MzQxMzRhMDRiMmJmN2RhMDBlZTZlN2Q4IiwidXNlcl9pZCI6Mn0.Zoydeht-cLUAe2z7CK5E2WqVJbhwloVRa-tqG_mONFk';
const VALID_REFRESH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1NDQyMzkwNywiaWF0IjoxNzUzODE5MTA3LCJqdGkiOiJkYzNmZjAxYmI3ZDQ0YzZlOTRiOTIwNzA1ZjFjZjEwNyIsInVzZXJfaWQiOjJ9.PlWWl5AcIVq50OaCQjIKl49hfpOd6yQQk1-wdGgvR64';

function migrateTokens() {
    console.log('🔍 Step 1: Checking current tokens...');
    
    // Check existing tokens
    const oldAccessToken = localStorage.getItem('access_token');
    const oldRefreshToken = localStorage.getItem('refresh_token');
    const newAccessToken = localStorage.getItem('token');
    const newRefreshToken = localStorage.getItem('refreshToken');
    
    console.log('Current token status:');
    console.log('  - access_token (old):', !!oldAccessToken);
    console.log('  - refresh_token (old):', !!oldRefreshToken);
    console.log('  - token (new):', !!newAccessToken);
    console.log('  - refreshToken (new):', !!newRefreshToken);
    
    console.log('\n🔄 Step 2: Setting valid tokens...');
    
    // Set the valid tokens
    localStorage.setItem('token', VALID_ACCESS_TOKEN);
    localStorage.setItem('refreshToken', VALID_REFRESH_TOKEN);
    
    // Also set user data for Admin user
    const adminUser = {
        id: 2,
        username: 'Admin',
        email: '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User',
        is_superuser: true,
        user_type: 'admin'
    };
    
    localStorage.setItem('user', JSON.stringify(adminUser));
    localStorage.setItem('user_type', 'admin');
    
    console.log('✅ Valid tokens set successfully!');
    console.log('✅ Admin user data set successfully!');
    
    console.log('\n🧪 Step 3: Testing API access...');
    
    // Test API call
    fetch('http://localhost:8000/api/schools/schools/', {
        headers: {
            'Authorization': `Bearer ${VALID_ACCESS_TOKEN}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    })
    .then(data => {
        console.log(`✅ API test successful! Found ${data.results?.length || 0} schools`);
        console.log('\n🎉 Migration complete! You can now:');
        console.log('   1. Refresh the page');
        console.log('   2. Navigate to EditStudent');
        console.log('   3. The school assignment dropdown should work');
    })
    .catch(error => {
        console.error('❌ API test failed:', error);
        console.log('⚠️ Migration completed but API test failed. Try refreshing the page.');
    });
}

function clearAllTokens() {
    console.log('🗑️ Clearing all authentication tokens...');
    
    const tokenKeys = [
        'token', 'access_token', 'refreshToken', 'refresh_token',
        'user', 'user_data', 'user_type'
    ];
    
    tokenKeys.forEach(key => {
        localStorage.removeItem(key);
        console.log(`   - Removed: ${key}`);
    });
    
    console.log('✅ All tokens cleared. Please log in again.');
}

// Auto-run migration
console.log('🚀 Running automatic token migration...');
migrateTokens();

// Export functions for manual use
window.migrateTokens = migrateTokens;
window.clearAllTokens = clearAllTokens;

console.log('\n📋 Available commands:');
console.log('   - migrateTokens(): Set valid tokens for testing');
console.log('   - clearAllTokens(): Clear all tokens and start fresh');

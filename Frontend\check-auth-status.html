<!DOCTYPE html>
<html>
<head>
    <title>Authentication Status Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 Authentication Status Check</h1>
    <div id="results"></div>
    
    <h2>Actions</h2>
    <button onclick="checkStatus()">🔄 Refresh Status</button>
    <button onclick="clearTokens()">🗑️ Clear All Tokens</button>
    <button onclick="testAPI()">🧪 Test API Call</button>
    
    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function checkStatus() {
            clearResults();
            addResult('🔍 Checking Authentication Status...', 'info');
            
            // Check all possible token keys
            const tokenKeys = [
                'token', 'access_token', 'refreshToken', 'refresh_token',
                'user', 'user_data', 'user_type'
            ];
            
            let hasValidTokens = false;
            
            tokenKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    if (key.includes('token')) {
                        const preview = value.length > 50 ? value.substring(0, 50) + '...' : value;
                        addResult(`✅ ${key}: ${preview}`, 'success');
                        hasValidTokens = true;
                    } else {
                        addResult(`✅ ${key}: ${value}`, 'success');
                    }
                } else {
                    addResult(`❌ ${key}: Not found`, 'warning');
                }
            });
            
            if (hasValidTokens) {
                addResult('🎉 Authentication tokens found!', 'success');
            } else {
                addResult('⚠️ No authentication tokens found. Please log in.', 'error');
            }
        }
        
        function clearTokens() {
            const tokenKeys = [
                'token', 'access_token', 'refreshToken', 'refresh_token',
                'user', 'user_data', 'user_type'
            ];
            
            tokenKeys.forEach(key => {
                localStorage.removeItem(key);
            });
            
            addResult('🗑️ All tokens cleared. Please log in again.', 'warning');
            checkStatus();
        }
        
        async function testAPI() {
            addResult('🧪 Testing API call...', 'info');
            
            const token = localStorage.getItem('token') || localStorage.getItem('access_token');
            
            if (!token) {
                addResult('❌ No token found for API test', 'error');
                return;
            }
            
            try {
                const response = await fetch('http://localhost:8000/api/schools/schools/', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ API test successful! Found ${data.results?.length || 0} schools`, 'success');
                } else {
                    addResult(`❌ API test failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ API test error: ${error.message}`, 'error');
            }
        }
        
        // Auto-check status on page load
        checkStatus();
    </script>
</body>
</html>

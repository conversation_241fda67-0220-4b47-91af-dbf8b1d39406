#!/usr/bin/env python
import os
import sys
import django
from datetime import date, datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from core.models import CustomUser
from schools.models import School, SchoolBranch
from users.models import Student as StudentProfile, Teacher as TeacherProfile, Staff
from academics.models import Department

def create_sample_data():
    print("🚀 Creating sample users for dashboard testing...")

    # Get Sample School and Main Campus (should already exist)
    try:
        sample_school = School.objects.get(name="Sample School")
        main_campus = SchoolBranch.objects.get(school=sample_school, name="Main Campus")
        print(f"✅ Found School: {sample_school.name}")
        print(f"✅ Found Branch: {main_campus.name}")
    except (School.DoesNotExist, SchoolBranch.DoesNotExist):
        print("❌ Sample School or Main Campus not found. Creating them...")
        sample_school, created = School.objects.get_or_create(
            name="Sample School",
            defaults={
                'code': 'SAMPLE',
                'address': '123 Education Street',
                'phone': '+254700000000',
                'email': '<EMAIL>',
                'website': 'https://sampleschool.edu',
                'registration_number': 'SAMPLE001',
                'established_date': date(2020, 1, 1),
                'is_active': True
            }
        )

        main_campus, created = SchoolBranch.objects.get_or_create(
            school=sample_school,
            name="Main Campus",
            defaults={
                'code': 'MAIN',
                'address': '123 Education Street, Main Building',
                'phone': '+254700000001',
                'email': '<EMAIL>',
                'website': 'https://main.sampleschool.edu',
                'registration_number': 'SAMPLE-MAIN-001',
                'established_date': date(2020, 1, 1),
                'is_active': True
            }
        )
        print(f"✅ Created School: {sample_school.name}")
        print(f"✅ Created Branch: {main_campus.name}")
    
    # Create a simple department for teachers
    dept, created = Department.objects.get_or_create(
        school=sample_school,
        school_branch=main_campus,
        code='GEN',
        defaults={'name': 'General Department'}
    )
    if created:
        print(f"✅ Created department: {dept.name}")

    # Create sample student users and profiles
    student_data = [
        {'username': 'student1', 'email': '<EMAIL>', 'first_name': 'Alice', 'last_name': 'Johnson'},
        {'username': 'student2', 'email': '<EMAIL>', 'first_name': 'Bob', 'last_name': 'Smith'},
        {'username': 'student3', 'email': '<EMAIL>', 'first_name': 'Carol', 'last_name': 'Davis'},
        {'username': 'student4', 'email': '<EMAIL>', 'first_name': 'David', 'last_name': 'Wilson'},
        {'username': 'student5', 'email': '<EMAIL>', 'first_name': 'Emma', 'last_name': 'Brown'},
    ]

    students_created = 0
    for student_info in student_data:
        # Create user
        user, user_created = CustomUser.objects.get_or_create(
            username=student_info['username'],
            defaults={
                'email': student_info['email'],
                'first_name': student_info['first_name'],
                'last_name': student_info['last_name'],
                'user_type': 'student',
                'is_active': True
            }
        )

        # Create student profile
        if user_created or not hasattr(user, 'student'):
            student_profile, profile_created = StudentProfile.objects.get_or_create(
                user=user,
                defaults={
                    'school_branch': main_campus,
                    'admission_number': f"STU{2025}{students_created + 1:03d}",
                    'date_of_birth': date(2010, 1, 1),
                    'gender': 'M' if students_created % 2 == 0 else 'F',
                    'admission_date': date.today()
                }
            )
            if profile_created:
                students_created += 1
                print(f"✅ Created student: {user.get_full_name()}")

    # Create sample teacher users and profiles
    teacher_data = [
        {'username': 'teacher1', 'email': '<EMAIL>', 'first_name': 'John', 'last_name': 'Anderson'},
        {'username': 'teacher2', 'email': '<EMAIL>', 'first_name': 'Sarah', 'last_name': 'Miller'},
        {'username': 'teacher3', 'email': '<EMAIL>', 'first_name': 'Michael', 'last_name': 'Taylor'},
    ]

    teachers_created = 0
    for teacher_info in teacher_data:
        # Create user
        user, user_created = CustomUser.objects.get_or_create(
            username=teacher_info['username'],
            defaults={
                'email': teacher_info['email'],
                'first_name': teacher_info['first_name'],
                'last_name': teacher_info['last_name'],
                'user_type': 'teacher',
                'is_active': True
            }
        )

        # Create teacher profile
        if user_created or not hasattr(user, 'teacher'):
            teacher_profile, profile_created = TeacherProfile.objects.get_or_create(
                user=user,
                defaults={
                    'school_branch': main_campus,
                    'teacher_number': f"TCH{2025}{teachers_created + 1:03d}",
                    'date_of_birth': date(1985, 1, 1),
                    'national_id': f"123456789{teachers_created}",
                    'phone_number': f"+25470000000{teachers_created}",
                    'email': teacher_info['email'],
                    'gender': 'M' if teachers_created % 2 == 0 else 'F'
                }
            )
            if profile_created:
                teachers_created += 1
                print(f"✅ Created teacher: {user.get_full_name()}")

    print(f"\n📊 Sample users created successfully!")
    print(f"🏫 School: {sample_school.name}")
    print(f"🏢 Branch: {main_campus.name}")
    print(f"👨‍🎓 Students created: {students_created}")
    print(f"👨‍🏫 Teachers created: {teachers_created}")
    print(f"📚 Department: {dept.name}")

    # Run the data check to verify
    print(f"\n=== VERIFICATION ===")
    total_students_in_branch = StudentProfile.objects.filter(school_branch=main_campus).count()
    total_teachers_in_branch = TeacherProfile.objects.filter(school_branch=main_campus).count()
    print(f"✅ Total students in {main_campus.name}: {total_students_in_branch}")
    print(f"✅ Total teachers in {main_campus.name}: {total_teachers_in_branch}")

if __name__ == "__main__":
    create_sample_data()

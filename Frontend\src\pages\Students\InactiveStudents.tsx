import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from 'react-redux';

import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "../../components/ui/table";
import Input from "../../components/form/input/InputField";
import Select from "../../components/form/Select";
import Button from "../../components/ui/button/Button";

import { studentService, type Student } from "../../services/studentService";
// Remove academicsService import since getGrades doesn't exist
// import academicsService, { type Grade } from "../../services/academicsService.ts";
import { RootState } from '../../store/store';

// Simple Grade interface for filtering
interface Grade {
  name: string;
  id: string;
}

export default function InactiveStudents() {
  const navigate = useNavigate();
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedGrade, setSelectedGrade] = useState("All");
  const [grades, setGrades] = useState<Grade[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalStudents, setTotalStudents] = useState(0);
  const [selectedStudents, setSelectedStudents] = useState<number[]>([]);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [sortBy, setSortBy] = useState<'name' | 'admission_number' | 'grade' | 'deactivation_date'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [statistics, setStatistics] = useState({
    totalInactive: 0,
    byGrade: {} as Record<string, number>,
    byGender: { male: 0, female: 0 },
    recentlyDeactivated: 0
  });
  const { user } = useSelector((state: RootState) => state.auth);
  const currentBranch = user?.school_branch;

  useEffect(() => {
    const timer = setTimeout(() => {
      fetchStudents();
    }, 500);

    return () => clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, selectedGrade, searchQuery]);

  // Extract unique grades from student data
  useEffect(() => {
    if (students.length > 0) {
      const uniqueGrades = Array.from(new Set(
        students
          .map(student => student.grade)
          .filter(grade => grade && grade.trim() !== '')
      )).map(grade => ({
        id: grade!,
        name: grade!
      }));

      setGrades(uniqueGrades);
    }
  }, [students]);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {
        page: currentPage,
        page_size: 10,
        is_active: false, // Only fetch inactive students
      };

      if (currentBranch) {
        params.school_branch = currentBranch;
      }

      if (selectedGrade !== "All") {
        params.grade = selectedGrade;
      }

      if (searchQuery) {
        params.search = searchQuery;
      }

      const data = await studentService.getAllStudents(params);
      const studentsData = data.results || [];
      setStudents(studentsData);
      setTotalStudents(data.count);
      setTotalPages(Math.ceil(data.count / 10));

      // Calculate statistics for inactive students
      const stats = {
        totalInactive: studentsData.length,
        byGrade: {} as Record<string, number>,
        byGender: {
          male: studentsData.filter(s => s.gender === 'M').length,
          female: studentsData.filter(s => s.gender === 'F').length
        },
        recentlyDeactivated: studentsData.filter(s => {
          if (!s.user?.date_joined) return false;
          const deactivationDate = new Date(s.user.date_joined);
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          return deactivationDate > thirtyDaysAgo;
        }).length
      };

      // Count by grade
      studentsData.forEach(student => {
        const grade = student.grade || 'Unknown';
        stats.byGrade[grade] = (stats.byGrade[grade] || 0) + 1;
      });

      setStatistics(stats);
    } catch (err: unknown) {
      const error = err as { response?: { data?: { message?: string } }, message?: string };
      console.error('Error:', error.response?.data || error.message);
      setError(error.response?.data?.message || "Failed to fetch students");
    } finally {
      setLoading(false);
    }
  };

  const handleClearSearch = () => {
    setSearchQuery("");
    setSelectedGrade("All");
    setCurrentPage(1);
  };

  const handleActivateStudent = async (studentId: number) => {
    try {
      await studentService.toggleStudentStatus(studentId, true);
      // Refresh the list after activation
      fetchStudents();
    } catch (err: unknown) {
      const error = err as { response?: { data?: { message?: string } }, message?: string };
      console.error('Error activating student:', error.response?.data || error.message);
      alert(`Failed to activate student: ${error.response?.data?.message || error.message || 'Unknown error'}`);
    }
  };

  // Bulk actions for inactive students
  const handleSelectAll = () => {
    if (selectedStudents.length === students.length) {
      setSelectedStudents([]);
    } else {
      setSelectedStudents(students.map(s => s.id).filter(id => id !== undefined) as number[]);
    }
  };

  const handleSelectStudent = (studentId: number) => {
    setSelectedStudents(prev =>
      prev.includes(studentId)
        ? prev.filter(id => id !== studentId)
        : [...prev, studentId]
    );
  };

  const handleBulkAction = async (action: 'reactivate' | 'export') => {
    if (selectedStudents.length === 0) {
      alert('Please select students first');
      return;
    }

    switch (action) {
      case 'reactivate':
        if (window.confirm(`Reactivate ${selectedStudents.length} selected students?`)) {
          try {
            for (const studentId of selectedStudents) {
              await studentService.toggleStudentStatus(studentId, true);
            }
            setSelectedStudents([]);
            fetchStudents();
            alert(`Successfully reactivated ${selectedStudents.length} students`);
          } catch (error) {
            console.error('Error reactivating students:', error);
            alert('Failed to reactivate some students');
          }
        }
        break;
      case 'export':
        handleExportSelected();
        break;
    }
  };

  const handleExportSelected = () => {
    const selectedStudentData = students.filter(s => selectedStudents.includes(s.id));
    const csvContent = generateCSV(selectedStudentData);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const fileName = `inactive_students_${new Date().toISOString().split('T')[0]}.csv`;

    // Create download link
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const generateCSV = (data: Student[]) => {
    const headers = ['Name', 'Admission Number', 'Gender', 'Grade', 'Class', 'Stream', 'Status'];
    const rows = data.map(student => [
      `${student.first_name} ${student.last_name}`,
      student.admission_number || '',
      student.gender === 'M' ? 'Male' : 'Female',
      student.grade || '',
      student.class_name || '',
      student.stream_name || '',
      'Inactive'
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  return (
    <div>
      <PageMeta title="Inactive Students | ShuleXcel" description="View and manage inactive students." />
      <PageBreadcrumb pageTitle="Inactive Students" />

      <div className="space-y-6">
        {/* Enhanced Header with Statistics */}
        <div className="bg-gradient-to-r from-red-600 to-orange-600 rounded-xl p-6 text-white">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-3xl font-bold mb-2">Inactive Students</h1>
              <p className="text-red-100 flex items-center">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                Students who have been deactivated from the system
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => navigate('/AllStudents')}
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                View Active Students
              </Button>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100 text-sm">Total Inactive</p>
                  <p className="text-2xl font-bold">{totalStudents}</p>
                </div>
                <div className="bg-white/20 rounded-full p-2">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100 text-sm">Male</p>
                  <p className="text-2xl font-bold text-blue-300">{statistics.byGender.male}</p>
                </div>
                <div className="bg-blue-500/20 rounded-full p-2">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100 text-sm">Female</p>
                  <p className="text-2xl font-bold text-pink-300">{statistics.byGender.female}</p>
                </div>
                <div className="bg-pink-500/20 rounded-full p-2">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100 text-sm">Recently Deactivated</p>
                  <p className="text-2xl font-bold text-yellow-300">{statistics.recentlyDeactivated}</p>
                </div>
                <div className="bg-yellow-500/20 rounded-full p-2">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Filters and Controls */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          {/* Enhanced Search & Filter */}
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Search and Filters */}
            <div className="flex-1 space-y-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <Input
                      type="text"
                      placeholder="Search inactive students by name, admission number..."
                      value={searchQuery}
                      onChange={(e) => {
                        setSearchQuery(e.target.value);
                        setCurrentPage(1);
                      }}
                      className="pl-10 w-full"
                    />
                    {searchQuery && (
                      <button
                        type="button"
                        onClick={handleClearSearch}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>

                <div className="flex gap-2">
                  <Select
                    options={[
                      { value: "All", label: "All Grades" },
                      ...grades.map(grade => ({ value: grade.name, label: grade.name }))
                    ]}
                    value={selectedGrade}
                    onChange={(value) => {
                      setSelectedGrade(value);
                      setCurrentPage(1);
                    }}
                    className="min-w-[120px]"
                  />
                </div>
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">View:</span>
                  <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                    <button
                      onClick={() => setViewMode('table')}
                      className={`px-3 py-1 rounded text-sm flex items-center gap-1 ${viewMode === 'table' ? 'bg-white shadow text-gray-900' : 'text-gray-600'}`}
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
                      </svg>
                      Table
                    </button>
                    <button
                      onClick={() => setViewMode('cards')}
                      className={`px-3 py-1 rounded text-sm flex items-center gap-1 ${viewMode === 'cards' ? 'bg-white shadow text-gray-900' : 'text-gray-600'}`}
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                      </svg>
                      Cards
                    </button>
                  </div>
                </div>
              </div>

              {/* Bulk Actions */}
              {selectedStudents.length > 0 && (
                <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                      {selectedStudents.length} inactive student(s) selected
                    </p>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBulkAction('reactivate')}
                        className="text-green-600 border-green-200 hover:bg-green-50"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Reactivate
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBulkAction('export')}
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Export Selected
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Student List - Table or Cards View */}
        {viewMode === 'table' ? (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableCell isHeader>
                    <input
                      type="checkbox"
                      checked={selectedStudents.length === students.length && students.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                    />
                  </TableCell>
                  <TableCell isHeader>#</TableCell>
                  <TableCell isHeader>Student Info</TableCell>
                  <TableCell isHeader>Academic Info</TableCell>
                  <TableCell isHeader>Status</TableCell>
                  <TableCell isHeader>Actions</TableCell>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-red-500 border-r-transparent"></div>
                      <p className="mt-2">Loading inactive students...</p>
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="text-red-600">
                        <svg className="w-12 h-12 mx-auto mb-4 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 className="font-medium text-lg">Error loading students</h3>
                        <p className="text-sm mt-1">{error}</p>
                        <button
                          type="button"
                          onClick={handleClearSearch}
                          className="mt-2 text-sm text-blue-600 hover:underline"
                        >
                          Clear filters and try again
                        </button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : students.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="text-gray-500">
                        <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 className="font-medium text-lg">No inactive students found</h3>
                        <p className="text-sm mt-1">Great! All students are currently active.</p>
                        {searchQuery && (
                          <button
                            type="button"
                            onClick={handleClearSearch}
                            className="mt-2 text-sm text-blue-600 hover:underline"
                          >
                            Clear search
                          </button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  students.map((student, index) => {
                    const studentKey = `inactive-${student.id || 'no-id'}-${student.admission_number || 'no-admission'}-${index}`;

                    return (
                      <TableRow key={studentKey} className="hover:bg-red-50 dark:hover:bg-red-900/10">
                        <TableCell>
                          <input
                            type="checkbox"
                            checked={selectedStudents.includes(student.id)}
                            onChange={() => handleSelectStudent(student.id)}
                            className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                          />
                        </TableCell>
                        <TableCell className="text-sm text-gray-500">
                          #{(currentPage - 1) * 10 + index + 1}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center text-white font-semibold">
                              {student.first_name?.charAt(0)}{student.last_name?.charAt(0)}
                            </div>
                            <div>
                              <div className="font-medium text-gray-900 dark:text-white">
                                {student.first_name} {student.last_name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {student.admission_number || 'No admission number'}
                              </div>
                              <div className="text-xs text-gray-400">
                                {student.gender === 'M' ? '👨 Male' : student.gender === 'F' ? '👩 Female' : '❓ Unknown'}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {student.grade || 'No Grade'}
                              </span>
                            </div>
                            <div className="text-sm text-gray-600">
                              📚 {student.class_name || 'No Class'}
                            </div>
                            <div className="text-xs text-gray-500">
                              🏫 {student.stream_name || 'No Stream'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              ❌ Inactive
                            </span>
                          </div>
                          {student.admission_date && (
                            <div className="text-xs text-gray-500 mt-1">
                              📅 {new Date(student.admission_date).toLocaleDateString()}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                      <div className="flex gap-2">
                        <button
                          type="button"
                          onClick={() => navigate(`/student/${student.id}`)}
                          className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                          title="View Details"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>
                        <button
                          type="button"
                          onClick={() => handleActivateStudent(student.id)}
                          className="p-1 text-green-600 hover:bg-green-50 rounded"
                          title="Activate Student"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </button>
                      </div>
                    </TableCell>
                  </TableRow>
                  );
                })
              )}
              </TableBody>
            </Table>
          </div>
        ) : (
          /* Card View */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {loading ? (
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 rounded"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              ))
            ) : error ? (
              <div className="col-span-full text-center py-12">
                <svg className="w-16 h-16 mx-auto mb-4 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-xl font-medium text-red-600 mb-2">Error loading students</p>
                <p className="text-gray-400">{error}</p>
                <button
                  type="button"
                  onClick={handleClearSearch}
                  className="mt-4 text-blue-600 hover:underline"
                >
                  Clear filters and try again
                </button>
              </div>
            ) : students.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p className="text-xl font-medium text-gray-500 mb-2">No inactive students found</p>
                <p className="text-gray-400">Great! All students are currently active.</p>
                {searchQuery && (
                  <button
                    type="button"
                    onClick={handleClearSearch}
                    className="mt-4 text-blue-600 hover:underline"
                  >
                    Clear search
                  </button>
                )}
              </div>
            ) : (
              students.map((student, index) => {
                const studentKey = `card-inactive-${student.id || 'no-id'}-${student.admission_number || 'no-admission'}-${index}`;

                return (
                  <div key={studentKey} className="bg-white dark:bg-gray-800 rounded-lg border border-red-200 dark:border-red-700 p-6 hover:shadow-lg transition-shadow">
                    {/* Card Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          checked={selectedStudents.includes(student.id)}
                          onChange={() => handleSelectStudent(student.id)}
                          className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                        />
                        <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                          {student.first_name?.charAt(0)}{student.last_name?.charAt(0)}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {student.first_name} {student.last_name}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {student.admission_number || 'No admission number'}
                          </p>
                        </div>
                      </div>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Inactive
                      </span>
                    </div>

                    {/* Card Content */}
                    <div className="space-y-3 mb-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Gender:</span>
                        <span className="text-sm font-medium">
                          {student.gender === 'M' ? '👨 Male' : student.gender === 'F' ? '👩 Female' : '❓ Unknown'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Grade:</span>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          {student.grade || 'No Grade'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Class:</span>
                        <span className="text-sm font-medium">{student.class_name || 'No Class'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Stream:</span>
                        <span className="text-sm font-medium">{student.stream_name || 'No Stream'}</span>
                      </div>
                      {student.admission_date && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Admission:</span>
                          <span className="text-sm font-medium">{new Date(student.admission_date).toLocaleDateString()}</span>
                        </div>
                      )}
                    </div>

                    {/* Card Actions */}
                    <div className="flex gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <button
                        type="button"
                        onClick={() => navigate(`/student/${student.id}`)}
                        className="flex-1 px-3 py-2 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
                      >
                        View Details
                      </button>
                      <button
                        type="button"
                        onClick={() => handleActivateStudent(student.id)}
                        className="flex-1 px-3 py-2 text-sm bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors"
                      >
                        Reactivate
                      </button>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        )}

        {/* Pagination */}
        {!loading && !error && students.length > 0 && (
          <div className="flex justify-between items-center p-6 border-t border-gray-200 dark:border-white/[0.05]">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Showing {(currentPage - 1) * 10 + 1} to {Math.min(currentPage * 10, totalStudents)} of {totalStudents} inactive students
            </div>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1 || loading}
                className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50 hover:bg-gray-300"
              >
                Previous
              </button>
              <button
                type="button"
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages || loading}
                className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50 hover:bg-gray-300"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

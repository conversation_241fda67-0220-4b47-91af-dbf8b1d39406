#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_comprehensive_student_update():
    print("🧪 Testing Comprehensive Student Update Functionality...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Get a student to test with
    student = Student.objects.first()
    if not student:
        print("❌ No students found")
        return
        
    print(f"\n📋 Testing comprehensive update for: {student.user.first_name} {student.user.last_name}")
    
    # Comprehensive test data
    test_data = {
        # User fields
        'first_name': 'Comprehensive',
        'last_name': 'TestStudent',
        'email': '<EMAIL>',
        'phone': '+254712345678',
        'is_active': True,
        
        # Personal information
        'gender': 'F' if student.gender == 'M' else 'M',
        'date_of_birth': '2008-05-15',
        'address': '123 Test Street, Nairobi, Kenya',
        'phone_number': '+254798765432',
        'emergency_contact': '+254711223344',
        'additional_notes': 'Test student with comprehensive data update',
        
        # Academic information
        'admission_number': f'COMP{student.admission_number}',
        'admission_date': '2024-01-15',
        'subject_type': 'Compulsory',
        
        # School information
        'school_branch_id': student.school_branch.id if student.school_branch else None,
    }
    
    print(f"\n🔄 Updating student with comprehensive data...")
    print(f"   - Personal: name, gender, DOB, address, contacts")
    print(f"   - Academic: admission info, subject type")
    print(f"   - Additional: notes and emergency contact")
    
    # Make API request
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    url = f'http://localhost:8000/api/users/students/{student.user.id}/'
    
    try:
        response = requests.patch(url, json=test_data, headers=headers, timeout=10)
        
        print(f"\n📡 API Response:")
        print(f"   - Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Comprehensive update successful!")
            
            # Refresh from database
            student.refresh_from_db()
            student.user.refresh_from_db()
            
            print(f"\n🔍 Verification Results:")
            
            # Check user fields
            print(f"👤 User Information:")
            print(f"   - Name: {student.user.first_name} {student.user.last_name}")
            print(f"   - Email: {student.user.email}")
            print(f"   - Phone: {student.user.phone}")
            print(f"   - Active: {student.user.is_active}")
            
            # Check personal fields
            print(f"\n🏠 Personal Information:")
            print(f"   - Gender: {student.gender}")
            print(f"   - DOB: {student.date_of_birth}")
            print(f"   - Address: {student.address}")
            print(f"   - Phone: {student.phone_number}")
            print(f"   - Emergency: {student.emergency_contact}")
            
            # Check academic fields
            print(f"\n🎓 Academic Information:")
            print(f"   - Admission #: {student.admission_number}")
            print(f"   - Subject Type: {student.subject_type}")
            
            # Check additional info
            print(f"\n📝 Additional Information:")
            print(f"   - Notes: {student.additional_notes}")
            
            # Verify changes
            verification_results = []
            
            # User fields
            if student.user.first_name == test_data['first_name']:
                verification_results.append("✅ First name updated")
            else:
                verification_results.append(f"❌ First name: expected '{test_data['first_name']}', got '{student.user.first_name}'")
                
            if student.user.email == test_data['email']:
                verification_results.append("✅ Email updated")
            else:
                verification_results.append(f"❌ Email: expected '{test_data['email']}', got '{student.user.email}'")
            
            # Personal fields
            if student.gender == test_data['gender']:
                verification_results.append("✅ Gender updated")
            else:
                verification_results.append(f"❌ Gender: expected '{test_data['gender']}', got '{student.gender}'")
                
            if str(student.date_of_birth) == test_data['date_of_birth']:
                verification_results.append("✅ Date of birth updated")
            else:
                verification_results.append(f"❌ DOB: expected '{test_data['date_of_birth']}', got '{student.date_of_birth}'")
                
            if student.address == test_data['address']:
                verification_results.append("✅ Address updated")
            else:
                verification_results.append(f"❌ Address: expected '{test_data['address']}', got '{student.address}'")
                
            if student.phone_number == test_data['phone_number']:
                verification_results.append("✅ Phone number updated")
            else:
                verification_results.append(f"❌ Phone: expected '{test_data['phone_number']}', got '{student.phone_number}'")
                
            if student.emergency_contact == test_data['emergency_contact']:
                verification_results.append("✅ Emergency contact updated")
            else:
                verification_results.append(f"❌ Emergency: expected '{test_data['emergency_contact']}', got '{student.emergency_contact}'")
                
            if student.additional_notes == test_data['additional_notes']:
                verification_results.append("✅ Additional notes updated")
            else:
                verification_results.append(f"❌ Notes: expected '{test_data['additional_notes']}', got '{student.additional_notes}'")
            
            # Academic fields
            if student.admission_number == test_data['admission_number']:
                verification_results.append("✅ Admission number updated")
            else:
                verification_results.append(f"❌ Admission: expected '{test_data['admission_number']}', got '{student.admission_number}'")
                
            if student.subject_type == test_data['subject_type']:
                verification_results.append("✅ Subject type updated")
            else:
                verification_results.append(f"❌ Subject type: expected '{test_data['subject_type']}', got '{student.subject_type}'")
            
            print(f"\n📊 Comprehensive Update Results:")
            for result in verification_results:
                print(f"   {result}")
                
            # Count successes
            successes = len([r for r in verification_results if r.startswith("✅")])
            total = len(verification_results)
            print(f"\n🎯 Overall Success Rate: {successes}/{total} ({(successes/total)*100:.1f}%)")
                
        else:
            print(f"❌ Update failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Error text: {response.text}")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")

if __name__ == "__main__":
    test_comprehensive_student_update()

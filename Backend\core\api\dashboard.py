from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count
from core.models import CustomUser
from schools.models import SchoolBranch
from library.models import Book, EResource
from inventory.models import Asset, Supply
from communication.models import Announcement, Message

class DashboardMetricsView(APIView):
    """
    API view to get dashboard metrics
    """
    permission_classes = [IsAuthenticated]
    
    def get_user_school_branch(self, user):
        """Helper method to get school branch from any user type"""
        # Check for Staff profile
        try:
            if hasattr(user, 'staff'):
                return user.staff.school_branch
        except:
            pass

        # Check for Teacher profile
        try:
            if hasattr(user, 'teacher'):
                return user.teacher.school_branch
        except:
            pass

        # Check for Student profile
        try:
            if hasattr(user, 'student'):
                return user.student.school_branch
        except:
            pass

        # Special handling for admin/staff users
        if user.is_staff or user.is_superuser:
            # Get the first available school branch for admin users
            return SchoolBranch.objects.first()

        return None

    def get(self, request):
        # Get the user's school branch
        school_branch = self.get_user_school_branch(request.user)

        if not school_branch:
            return Response(
                {"error": "User is not associated with any school branch"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get metrics based on the user's school branch
        try:
            # Import here to avoid circular imports
            from users.models import Student, Teacher, Staff

            # Count users by their profiles
            students_count = Student.objects.filter(school_branch=school_branch).count()
            teachers_count = Teacher.objects.filter(school_branch=school_branch).count()
            staff_count = Staff.objects.filter(school_branch=school_branch).count()
            total_users = students_count + teachers_count + staff_count

            metrics = {
                "users": {
                    "total": total_users,
                    "students": students_count,
                    "teachers": teachers_count,
                    "staff": staff_count,
                    "active": total_users,  # Assume all are active for now
                },
                "library": {
                    "books": Book.objects.filter(school_branch=school_branch).count() if hasattr(Book, 'school_branch') else 0,
                    "e_resources": EResource.objects.filter(school_branch=school_branch).count() if hasattr(EResource, 'school_branch') else 0,
                },
                "inventory": {
                    "assets": Asset.objects.filter(school_branch=school_branch).count() if hasattr(Asset, 'school_branch') else 0,
                    "supplies": Supply.objects.filter(school_branch=school_branch).count() if hasattr(Supply, 'school_branch') else 0,
                },
                "communication": {
                    "announcements": Announcement.objects.filter(school_branch=school_branch).count() if hasattr(Announcement, 'school_branch') else 0,
                    "messages": Message.objects.filter(school_branch=school_branch).count() if hasattr(Message, 'school_branch') else 0,
                }
            }
        except Exception as e:
            # Fallback metrics if there are any import or query issues
            metrics = {
                "users": {"total": 0, "students": 0, "teachers": 0, "staff": 0, "active": 0},
                "library": {"books": 0, "e_resources": 0},
                "inventory": {"assets": 0, "supplies": 0},
                "communication": {"announcements": 0, "messages": 0},
                "error": str(e)
            }
        
        return Response(metrics, status=status.HTTP_200_OK)

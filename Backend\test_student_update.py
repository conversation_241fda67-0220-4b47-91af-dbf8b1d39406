#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def test_student_update():
    print("🧪 Testing Student Update Functionality...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return

    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username} (type: {admin_user.user_type})")
    
    # Get a student to test with
    student = Student.objects.first()
    if not student:
        print("❌ No students found")
        return
        
    print(f"\n📋 Testing with student: {student.user.first_name} {student.user.last_name}")
    print(f"   - Student ID: {student.user.id}")
    print(f"   - Admission Number: {student.admission_number}")
    print(f"   - Current Gender: {student.gender}")
    
    # Test data to update
    test_data = {
        'first_name': 'Updated',
        'last_name': 'TestName',
        'gender': 'F' if student.gender == 'M' else 'M',
        'admission_number': f"TEST{student.admission_number}"
    }
    
    print(f"\n🔄 Updating student with data: {test_data}")
    
    # Make API request
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    url = f'http://localhost:8000/api/users/students/{student.user.id}/'
    
    try:
        response = requests.patch(url, json=test_data, headers=headers, timeout=10)
        
        print(f"\n📡 API Response:")
        print(f"   - Status: {response.status_code}")
        print(f"   - Response: {response.text[:500]}")
        
        if response.status_code == 200:
            print("✅ Update request successful!")
            
            # Refresh from database
            student.refresh_from_db()
            student.user.refresh_from_db()
            
            print(f"\n🔍 After update:")
            print(f"   - Name: {student.user.first_name} {student.user.last_name}")
            print(f"   - Gender: {student.gender}")
            print(f"   - Admission: {student.admission_number}")
            
            # Check if changes were applied
            changes_applied = []
            if student.user.first_name == test_data['first_name']:
                changes_applied.append("✅ First name updated")
            else:
                changes_applied.append(f"❌ First name not updated: expected '{test_data['first_name']}', got '{student.user.first_name}'")
                
            if student.user.last_name == test_data['last_name']:
                changes_applied.append("✅ Last name updated")
            else:
                changes_applied.append(f"❌ Last name not updated: expected '{test_data['last_name']}', got '{student.user.last_name}'")
                
            if student.gender == test_data['gender']:
                changes_applied.append("✅ Gender updated")
            else:
                changes_applied.append(f"❌ Gender not updated: expected '{test_data['gender']}', got '{student.gender}'")
                
            if student.admission_number == test_data['admission_number']:
                changes_applied.append("✅ Admission number updated")
            else:
                changes_applied.append(f"❌ Admission number not updated: expected '{test_data['admission_number']}', got '{student.admission_number}'")
            
            print(f"\n📊 Update Results:")
            for result in changes_applied:
                print(f"   {result}")
                
        else:
            print(f"❌ Update failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Error text: {response.text}")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")

if __name__ == "__main__":
    test_student_update()

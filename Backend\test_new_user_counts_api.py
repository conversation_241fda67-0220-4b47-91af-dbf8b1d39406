#!/usr/bin/env python
import os
import sys
import django
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from schools.models import School, SchoolBranch

def test_new_user_counts_api():
    print("🧪 Testing NEW User Counts API...")
    
    base_url = "http://localhost:8000/api/core/users/counts/"
    
    # Get all schools and branches
    schools = School.objects.all()
    branches = SchoolBranch.objects.all()
    
    print(f"\n📋 Available Schools:")
    for school in schools:
        print(f"   - {school.name} (ID: {school.id})")
    
    print(f"\n📋 Available Branches:")
    for branch in branches:
        print(f"   - {branch.name} (ID: {branch.id}) - School: {branch.school.name}")
    
    # Test different combinations
    test_cases = [
        {"name": "No filters (global)", "params": {}},
        {"name": "Sample School only", "params": {"school": 3}},
        {"name": "Sample School - Main Campus", "params": {"school": 3, "branch": 5}},
        {"name": "MAKINI SCHOOL only", "params": {"school": 1}},
        {"name": "MAKINI SCHOOL - Main Branch", "params": {"school": 1, "branch": 2}},
    ]
    
    for test_case in test_cases:
        print(f"\n🔍 Testing: {test_case['name']}")
        print(f"   Parameters: {test_case['params']}")
        
        try:
            response = requests.get(base_url, params=test_case['params'], timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ SUCCESS: {response.status_code}")
                print(f"   📊 Results:")
                print(f"      - Students: {data.get('totalStudents', 0)}")
                print(f"      - Teachers: {data.get('totalTeachers', 0)}")
                print(f"      - Staff: {data.get('totalStaff', 0)}")
                print(f"      - Admins: {data.get('totalAdmins', 0)}")
                print(f"      - Parents: {data.get('totalParents', 0)}")
                print(f"      - Total: {data.get('total', 0)}")
            elif response.status_code == 401:
                print(f"   🔐 AUTHENTICATION REQUIRED: {response.status_code}")
            else:
                print(f"   ❌ ERROR: {response.status_code} - {response.text[:200]}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ CONNECTION ERROR: {str(e)}")

if __name__ == "__main__":
    test_new_user_counts_api()

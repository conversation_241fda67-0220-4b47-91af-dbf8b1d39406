from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .dashboard import DashboardMetricsView
from .user_counts import UserCountsView
from ..group_views import GroupViewSet, PermissionViewSet
from .views import CustomUserViewSet

router = DefaultRouter()
router.register(r'groups', GroupViewSet)
router.register(r'permissions', PermissionViewSet)
router.register(r'users', CustomUserViewSet)

urlpatterns = [
    path('dashboard/metrics/', DashboardMetricsView.as_view(), name='dashboard-metrics'),
    path('users/counts/', UserCountsView.as_view(), name='user-counts'),
    path('', include(router.urls)),
]

#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student, SchoolBranch
from core.models import CustomUser

def test_school_assignment():
    print("🔍 Testing School Assignment Functionality...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    # Get an unassigned student
    unassigned_student = Student.objects.filter(school_branch__isnull=True).first()
    if not unassigned_student:
        print("❌ No unassigned students found")
        return
        
    print(f"\n📋 Testing assignment for: {unassigned_student.user.first_name} {unassigned_student.user.last_name}")
    print(f"   - Current assignment: {unassigned_student.school_branch}")
    
    # Get a school branch to assign to
    branch = SchoolBranch.objects.first()
    if not branch:
        print("❌ No school branches found")
        return
        
    print(f"   - Target branch: {branch.name} ({branch.school.name if branch.school else 'No School'})")
    
    # Test assignment via API
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    url = f'http://localhost:8000/api/users/students/{unassigned_student.user.id}/'
    
    assignment_data = {
        'school_branch_id': branch.id
    }
    
    try:
        print(f"\n🔄 Attempting assignment via API...")
        response = requests.patch(url, json=assignment_data, headers=headers, timeout=10)
        
        print(f"📡 API Response:")
        print(f"   - Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ Assignment successful!")
            
            # Refresh from database
            unassigned_student.refresh_from_db()
            
            print(f"\n🔍 Verification:")
            print(f"   - New assignment: {unassigned_student.school_branch}")
            print(f"   - Branch ID: {unassigned_student.school_branch.id if unassigned_student.school_branch else 'None'}")
            print(f"   - Expected ID: {branch.id}")
            
            if unassigned_student.school_branch and unassigned_student.school_branch.id == branch.id:
                print(f"✅ Assignment verified successfully!")
                print(f"🎯 School assignment functionality is working!")
            else:
                print(f"❌ Assignment verification failed")
                
        else:
            print(f"❌ Assignment failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Error text: {response.text[:200]}")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")
    
    # Test fetching schools and branches for dropdowns
    print(f"\n🔍 Testing dropdown data APIs...")
    
    # Test schools API
    schools_url = 'http://localhost:8000/api/schools/schools/'
    try:
        schools_response = requests.get(schools_url, headers=headers, timeout=10)
        if schools_response.status_code == 200:
            schools_data = schools_response.json()
            schools_count = len(schools_data.get('results', []))
            print(f"   ✅ Schools API: {schools_count} schools available")
        else:
            print(f"   ❌ Schools API failed: {schools_response.status_code}")
    except Exception as e:
        print(f"   ❌ Schools API error: {str(e)}")
    
    # Test branches API
    branches_url = 'http://localhost:8000/api/schools/branch/'
    try:
        branches_response = requests.get(branches_url, headers=headers, timeout=10)
        if branches_response.status_code == 200:
            branches_data = branches_response.json()
            branches_count = len(branches_data.get('results', []))
            print(f"   ✅ Branches API: {branches_count} branches available")
            
            # Show sample branch data
            if branches_count > 0:
                sample_branch = branches_data['results'][0]
                print(f"   📋 Sample branch: {sample_branch.get('name')} (School: {sample_branch.get('school', {}).get('name', 'No School')})")
        else:
            print(f"   ❌ Branches API failed: {branches_response.status_code}")
    except Exception as e:
        print(f"   ❌ Branches API error: {str(e)}")

    print(f"\n🎯 School assignment test complete!")

if __name__ == "__main__":
    test_school_assignment()

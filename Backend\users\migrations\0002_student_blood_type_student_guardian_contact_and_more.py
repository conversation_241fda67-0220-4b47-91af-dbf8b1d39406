# Generated by Django 5.2.2 on 2025-07-30 04:06

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='student',
            name='blood_type',
            field=models.CharField(blank=True, choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')], help_text="Student's blood type", max_length=3, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='guardian_contact',
            field=models.CharField(blank=True, help_text='Primary contact number for guardian', max_length=15, null=True, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')]),
        ),
        migrations.AddField(
            model_name='student',
            name='guardian_email',
            field=models.EmailField(blank=True, help_text="Guardian's email address", max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='guardian_name',
            field=models.CharField(blank=True, help_text='Full name of parent/guardian', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='guardian_occupation',
            field=models.CharField(blank=True, help_text="Guardian's occupation", max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='guardian_relationship',
            field=models.CharField(blank=True, choices=[('Father', 'Father'), ('Mother', 'Mother'), ('Guardian', 'Guardian'), ('Grandfather', 'Grandfather'), ('Grandmother', 'Grandmother'), ('Uncle', 'Uncle'), ('Aunt', 'Aunt'), ('Other', 'Other')], help_text='Relationship to student', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='medical_conditions',
            field=models.TextField(blank=True, help_text='Medical conditions, allergies, or special medical needs', null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='nationality',
            field=models.CharField(blank=True, help_text="Student's nationality", max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='religion',
            field=models.CharField(blank=True, help_text="Student's religion", max_length=50, null=True),
        ),
    ]

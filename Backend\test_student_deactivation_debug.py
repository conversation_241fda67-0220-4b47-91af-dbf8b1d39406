#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from users.models import Student
from core.models import CustomUser

def debug_student_deactivation():
    print("🔍 Debugging Student Deactivation Issue...")
    
    # Get a superuser token for testing
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ No superuser found")
        return
        
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    print(f"✅ Using token for user: {admin_user.username}")
    
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    
    # Get an active student for testing
    active_student = Student.objects.filter(user__is_active=True).first()
    if not active_student:
        print("❌ No active students found for testing")
        return
    
    print(f"\n📋 Testing with student: {active_student.user.first_name} {active_student.user.last_name}")
    print(f"   📋 Student User ID: {active_student.user.id}")
    print(f"   📋 Student Profile ID: {active_student.pk}")
    print(f"   📋 Current status: {'Active' if active_student.user.is_active else 'Inactive'}")
    print(f"   📋 User type: {active_student.user.user_type}")
    
    # Test different deactivation approaches
    test_approaches = [
        {
            'name': 'Frontend approach (is_active field)',
            'url': f'http://localhost:8000/api/users/students/{active_student.user.id}/',
            'data': {'is_active': False}
        },
        {
            'name': 'User nested approach',
            'url': f'http://localhost:8000/api/users/students/{active_student.user.id}/',
            'data': {'user': {'is_active': False}}
        },
        {
            'name': 'Direct user update',
            'url': f'http://localhost:8000/api/core/users/{active_student.user.id}/',
            'data': {'is_active': False}
        }
    ]
    
    for i, approach in enumerate(test_approaches, 1):
        print(f"\n🔍 Test {i}: {approach['name']}")
        print(f"   📡 URL: {approach['url']}")
        print(f"   📋 Data: {approach['data']}")
        
        try:
            response = requests.patch(approach['url'], json=approach['data'], headers=headers, timeout=10)
            print(f"   📡 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ API call successful!")
                
                # Check response data
                try:
                    response_data = response.json()
                    if 'user' in response_data:
                        user_active = response_data['user'].get('is_active')
                        print(f"   📋 Response user.is_active: {user_active}")
                    elif 'is_active' in response_data:
                        user_active = response_data.get('is_active')
                        print(f"   📋 Response is_active: {user_active}")
                    else:
                        print(f"   📋 Response keys: {list(response_data.keys())}")
                except:
                    print(f"   📋 Response: {response.text[:200]}")
                
                # Verify in database
                active_student.user.refresh_from_db()
                db_status = active_student.user.is_active
                print(f"   📋 Database status after update: {'Active' if db_status else 'Inactive'}")
                
                if not db_status:
                    print(f"   ✅ SUCCESS: Student deactivated!")
                    # Reactivate for next test
                    active_student.user.is_active = True
                    active_student.user.save()
                    print(f"   🔄 Reactivated for next test")
                    break
                else:
                    print(f"   ❌ FAILED: Student still active in database")
                    
            elif response.status_code == 404:
                print(f"   ❌ Not Found: Endpoint doesn't exist")
            elif response.status_code == 403:
                print(f"   ❌ Forbidden: Permission denied")
            elif response.status_code == 400:
                print(f"   ❌ Bad Request: {response.text[:200]}")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                print(f"   Error: {response.text[:200]}")
                
        except Exception as e:
            print(f"   ❌ Request failed: {str(e)}")
    
    # Check what endpoints are available
    print(f"\n🔍 Checking available endpoints...")
    
    endpoints_to_check = [
        f'/api/users/students/{active_student.user.id}/',
        f'/api/core/users/{active_student.user.id}/',
        f'/api/users/students/{active_student.pk}/',
    ]
    
    for endpoint in endpoints_to_check:
        try:
            url = f'http://localhost:8000{endpoint}'
            response = requests.get(url, headers=headers, timeout=5)
            print(f"   📡 GET {endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'user' in data:
                        print(f"      - Has 'user' field: {type(data['user'])}")
                    if 'is_active' in data:
                        print(f"      - Has 'is_active' field: {data['is_active']}")
                except:
                    pass
        except:
            print(f"   ❌ GET {endpoint}: Failed")
    
    print(f"\n🎯 Deactivation debug complete!")

if __name__ == "__main__":
    debug_student_deactivation()
